"use strict";const e=require("../../common/vendor.js"),o=require("../../utils/api.js");require("../../utils/config.js");const s={data:()=>({loading:!1,showRegisterModal:!1,showDevTools:!1,loginForm:{username:"",password:""},registerForm:{username:"",password:"",nickname:""},testUsers:[{username:"test",password:"test123",nickname:"测试用户",color:"#667eea"},{username:"zhang",password:"123456",nickname:"张三",color:"#f093fb"},{username:"li",password:"123456",nickname:"李四",color:"#4facfe"}]}),methods:{async handleLogin(){if(this.loginForm.username&&this.loginForm.password){this.loading=!0;try{console.log("=== 开始登录 ==="),console.log("登录表单数据:",this.loginForm),console.log("API地址:","http://localhost:8080/api/auth/login");const s=await o.login(this.loginForm);console.log("登录API响应:",s),200===s.code?(e.index.setStorageSync("token",s.data.token),e.index.setStorageSync("userInfo",s.data.user),console.log("登录成功，保存的用户信息:",s.data.user),e.index.showToast({title:"登录成功",icon:"success"}),e.index.reLaunch({url:"/pages/chat/chat"})):(console.log("登录失败:",s.message),e.index.showToast({title:s.message||"登录失败",icon:"none"}))}catch(s){console.error("登录异常:",s),e.index.showToast({title:"网络错误，请检查后端服务: "+s.message,icon:"none"})}finally{this.loading=!1}}else e.index.showToast({title:"请填写完整信息",icon:"none"})},handleRegister(){this.showRegisterModal=!0},closeRegisterPopup(){this.showRegisterModal=!1,this.registerForm={username:"",password:"",nickname:""}},async submitRegister(){if(this.registerForm.username&&this.registerForm.password)try{const s=await o.register(this.registerForm);200===s.code?(e.index.showToast({title:"注册成功",icon:"success"}),this.closeRegisterPopup(),this.loginForm.username=this.registerForm.username,this.loginForm.password=this.registerForm.password):e.index.showToast({title:s.message,icon:"none"})}catch(s){e.index.showToast({title:"注册失败",icon:"none"})}else e.index.showToast({title:"请填写完整信息",icon:"none"})},async testConnection(){e.index.showLoading({title:"测试连接中..."});try{await e.index.request({url:"http://localhost:8080/api/test",method:"GET",timeout:5e3});e.index.hideLoading(),e.index.showToast({title:"连接成功",icon:"success"})}catch(o){e.index.hideLoading(),e.index.showToast({title:"连接失败",icon:"none"})}},async quickLogin(e){this.loginForm.username=e.username,this.loginForm.password=e.password,await this.handleLogin()},toggleDevTools(){this.showDevTools=!this.showDevTools}}};const t=e._export_sfc(s,[["render",function(o,s,t,i,n,r){return e.e({a:e.o((...e)=>r.toggleDevTools&&r.toggleDevTools(...e)),b:n.loginForm.username,c:e.o(e=>n.loginForm.username=e.detail.value),d:n.loginForm.password,e:e.o(e=>n.loginForm.password=e.detail.value),f:n.loading},(n.loading,{}),{g:e.t(n.loading?"登录中...":"开始AI之旅"),h:e.o((...e)=>r.handleLogin&&r.handleLogin(...e)),i:n.loading,j:e.o((...e)=>r.handleRegister&&r.handleRegister(...e)),k:n.showDevTools},n.showDevTools?{l:e.o((...e)=>r.testConnection&&r.testConnection(...e)),m:e.f(n.testUsers,(o,s,t)=>({a:e.t(o.nickname.charAt(0)),b:o.color,c:e.t(o.nickname),d:e.t(o.username),e:e.t(o.password),f:o.username,g:e.o(e=>r.quickLogin(o),o.username)}))}:{},{n:n.showRegisterModal},n.showRegisterModal?{o:n.registerForm.username,p:e.o(e=>n.registerForm.username=e.detail.value),q:n.registerForm.password,r:e.o(e=>n.registerForm.password=e.detail.value),s:n.registerForm.nickname,t:e.o(e=>n.registerForm.nickname=e.detail.value),v:e.o((...e)=>r.closeRegisterPopup&&r.closeRegisterPopup(...e)),w:e.o((...e)=>r.submitRegister&&r.submitRegister(...e)),x:e.o(()=>{}),y:e.o((...e)=>r.closeRegisterPopup&&r.closeRegisterPopup(...e))}:{})}],["__scopeId","data-v-a637cc87"]]);wx.createPage(t);
