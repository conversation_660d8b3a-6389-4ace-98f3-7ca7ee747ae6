"use strict";const e=require("../../common/vendor.js"),t=require("../../utils/api.js");require("../../utils/config.js");const s={data:()=>({friendRequests:[],loading:!1}),onLoad(){this.loadFriendRequests()},onShow(){this.loadFriendRequests()},methods:{goBack(){e.index.navigateBack()},async loadFriendRequests(){this.loading=!0;try{const s=e.index.getStorageSync("userInfo");if(!s)return void e.index.showToast({title:"请先登录",icon:"none"});const n=await t.getReceivedFriendRequests(s.userId);console.log("好友申请列表:",n),200===n.code?this.friendRequests=n.data:e.index.showToast({title:n.message||"加载失败",icon:"none"})}catch(s){console.error("加载好友申请失败:",s),e.index.showToast({title:"网络错误，请重试",icon:"none"})}finally{this.loading=!1}},async handleRequest(s,n){try{const a=e.index.getStorageSync("userInfo");if(!a)return void e.index.showToast({title:"请先登录",icon:"none"});const o=await t.handleFriendRequest({requestId:s.id,action:n,userId:a.userId});console.log("处理好友申请结果:",o),200===o.code?(s.status=n,this.$forceUpdate(),e.index.showToast({title:"ACCEPTED"===n?"已同意好友申请":"已拒绝好友申请",icon:"success"}),"ACCEPTED"===n&&e.index.$emit("friendListUpdate")):e.index.showToast({title:o.message||"操作失败",icon:"none"})}catch(a){console.error("处理好友申请失败:",a),e.index.showToast({title:"网络错误，请重试",icon:"none"})}},getAvatarText:e=>e.nickname?e.nickname.charAt(0):e.username.charAt(0),formatTime(e){if(!e)return"";const t=new Date(e),s=new Date-t;return s<6e4?"刚刚":s<36e5?Math.floor(s/6e4)+"分钟前":s<864e5?Math.floor(s/36e5)+"小时前":t.toLocaleDateString()},getStatusText(e){switch(e){case"ACCEPTED":return"已同意";case"REJECTED":return"已拒绝";case"PENDING":return"待处理";default:return"未知状态"}},getStatusClass(e){switch(e){case"ACCEPTED":return"status-accepted";case"REJECTED":return"status-rejected";default:return"status-pending"}}}};const n=e._export_sfc(s,[["render",function(t,s,n,a,o,i){return e.e({a:e.o((...e)=>i.goBack&&i.goBack(...e)),b:o.friendRequests.length>0},o.friendRequests.length>0?{c:e.f(o.friendRequests,(t,s,n)=>e.e({a:e.t(i.getAvatarText(t.fromUser)),b:e.t(t.fromUser.nickname||t.fromUser.username),c:e.t(t.message||"请求添加您为好友"),d:e.t(i.formatTime(t.createTime)),e:"PENDING"===t.status},"PENDING"===t.status?{f:e.o(e=>i.handleRequest(t,"REJECTED"),t.id),g:e.o(e=>i.handleRequest(t,"ACCEPTED"),t.id)}:{h:e.t(i.getStatusText(t.status)),i:e.n(i.getStatusClass(t.status))},{j:t.id}))}:(o.loading,{}),{d:!o.loading,e:o.loading},(o.loading,{}))}],["__scopeId","data-v-e271fe7d"]]);wx.createPage(n);
