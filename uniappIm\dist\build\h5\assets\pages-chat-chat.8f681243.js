import{_ as e,o as t,c as a,w as s,i as n,a as o,b as i,d,e as l,r as u,F as c,t as r,f as h,g as f,S as g,j as m}from"./index-eb56c588.js";import{g as _,a as C}from"./api.2bc223ab.js";import{c as k,a as p}from"./websocket.b01ea1d9.js";import{C as M}from"./CustomTabBar.ebabc49f.js";const L=e({components:{CustomTabBar:M},data:()=>({userInfo:null,userList:[],showAddMenuModal:!1,pendingRequestCount:0,loading:!1}),onLoad(){this.checkLogin(),this.loadUserInfo(),this.loadFriendList(),this.loadPendingRequestCount(),this.initWebSocket()},onUnload(){p()},onShow(){this.loadFriendList(),this.loadPendingRequestCount()},methods:{checkLogin(){uni.getStorageSync("token")||uni.reLaunch({url:"/pages/login/login"})},loadUserInfo(){this.userInfo=uni.getStorageSync("userInfo")},async loadFriendList(){this.loading=!0;try{console.log("=== 开始加载好友列表 ===");const e=await _();if(console.log("好友列表API响应:",e),200===e.code){const t=e.data||[];console.log("原始好友数据:",t),this.userList=t.map(e=>({...e,id:e.userId||e.id,userId:e.userId||e.id,nickname:e.nickname||e.username,username:e.username,lastMessage:"点击开始聊天",lastMessageTime:new Date,unreadCount:0})),console.log("处理后的好友列表:",this.userList)}else console.error("获取好友列表失败:",e.message),uni.showToast({title:e.message||"获取好友列表失败",icon:"none"})}catch(e){console.error("获取好友列表失败:",e),uni.showToast({title:"网络错误: "+e.message,icon:"none"})}finally{this.loading=!1}},async loadPendingRequestCount(){try{const e=await C();200===e.code&&(this.pendingRequestCount=e.data||0)}catch(e){console.error("获取待处理申请数量失败:",e)}},initWebSocket(){const e=uni.getStorageSync("token");e&&k(e,e=>{if(console.log("收到消息:",e),"chat"===e.type){const t=this.userList.findIndex(t=>(t.userId||t.id)===e.fromUserId);if(-1!==t){this.userList[t].lastMessage=e.content,this.userList[t].lastMessageTime=new Date,this.userList[t].unreadCount=(this.userList[t].unreadCount||0)+1;const a=this.userList.splice(t,1)[0];this.userList.unshift(a)}}})},openChat(e){e.unreadCount=0;const t=e.userId||e.id,a=e.nickname||e.username||"未知用户";console.log("打开聊天:",{userId:t,nickname:a,user:e}),uni.navigateTo({url:`/pages/chatDetail/chatDetail?userId=${t}&nickname=${encodeURIComponent(a)}`})},showAddMenu(){this.showAddMenuModal=!0},hideAddMenu(){this.showAddMenuModal=!1},addFriend(){this.hideAddMenu(),uni.navigateTo({url:"/pages/addFriend/addFriend"})},openFriendManage(){this.hideAddMenu(),uni.navigateTo({url:"/pages/friendRequests/friendRequests"})},logout(){this.hideAddMenu(),uni.showModal({title:"提示",content:"确定要退出登录吗？",success:e=>{e.confirm&&(uni.removeStorageSync("token"),uni.removeStorageSync("userInfo"),uni.reLaunch({url:"/pages/login/login"}))}})},formatTime(e){if(!e)return"";const t=new Date,a=new Date(e),s=t-a;if(s<6e4)return"刚刚";if(s<36e5)return Math.floor(s/6e4)+"分钟前";if(s<864e5)return Math.floor(s/36e5)+"小时前";const n=new Date(t.getFullYear(),t.getMonth(),t.getDate()),o=new Date(n.getTime()-864e5);return a>=n?a.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit"}):a>=o?"昨天":a.toLocaleDateString("zh-CN",{month:"2-digit",day:"2-digit"})}}},[["render",function(e,_,C,k,p,M){const L=f,y=n,w=g,I=m("CustomTabBar");return t(),a(y,{class:"chat-container"},{default:s(()=>[o(" 顶部导航栏 "),i(y,{class:"header"},{default:s(()=>[i(y,{class:"header-content"},{default:s(()=>[i(L,{class:"title"},{default:s(()=>[d("即时通讯")]),_:1}),i(y,{class:"header-right"},{default:s(()=>[i(y,{class:"add-icon",onClick:M.showAddMenu},{default:s(()=>[i(L,{class:"icon"},{default:s(()=>[d("+")]),_:1})]),_:1},8,["onClick"])]),_:1})]),_:1})]),_:1}),o(" 聊天列表 "),i(w,{class:"chat-list","scroll-y":"true"},{default:s(()=>[(t(!0),l(c,null,u(p.userList,(e,n)=>(t(),a(y,{key:e.id,class:"chat-item",onClick:t=>M.openChat(e)},{default:s(()=>[i(y,{class:"avatar"},{default:s(()=>[i(L,{class:"avatar-text"},{default:s(()=>[d(r(e.nickname.charAt(0)),1)]),_:2},1024)]),_:2},1024),i(y,{class:"chat-content"},{default:s(()=>[i(y,{class:"chat-header"},{default:s(()=>[i(L,{class:"friend-name"},{default:s(()=>[d(r(e.nickname),1)]),_:2},1024),i(L,{class:"chat-time"},{default:s(()=>[d(r(M.formatTime(e.lastMessageTime)),1)]),_:2},1024)]),_:2},1024),i(L,{class:"last-message"},{default:s(()=>[d(r(e.lastMessage||"点击开始聊天"),1)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onClick"]))),128))]),_:1}),o(" 加载状态 "),p.loading?(t(),a(y,{key:0,class:"loading-state"},{default:s(()=>[i(L,null,{default:s(()=>[d("加载中...")]),_:1})]),_:1})):o("v-if",!0),o(" 空状态 "),p.loading||0!==p.userList.length?o("v-if",!0):(t(),a(y,{key:1,class:"empty-state"},{default:s(()=>[i(L,{class:"empty-text"},{default:s(()=>[d("暂无好友")]),_:1}),i(L,{class:"empty-hint"},{default:s(()=>[d('点击右上角"+"添加好友开始聊天')]),_:1})]),_:1})),o(" 添加菜单弹窗 "),p.showAddMenuModal?(t(),a(y,{key:2,class:"add-menu-modal",onClick:M.hideAddMenu},{default:s(()=>[i(y,{class:"add-menu",onClick:_[0]||(_[0]=h(()=>{},["stop"]))},{default:s(()=>[i(y,{class:"add-menu-item",onClick:M.addFriend},{default:s(()=>[i(y,{class:"menu-item-content"},{default:s(()=>[i(L,{class:"add-menu-icon"},{default:s(()=>[d("👤")]),_:1}),i(L,{class:"add-menu-text"},{default:s(()=>[d("添加好友")]),_:1})]),_:1})]),_:1},8,["onClick"]),i(y,{class:"add-menu-item",onClick:M.openFriendManage},{default:s(()=>[i(y,{class:"menu-item-content"},{default:s(()=>[i(L,{class:"add-menu-icon"},{default:s(()=>[d("👥")]),_:1}),i(L,{class:"add-menu-text"},{default:s(()=>[d("好友申请")]),_:1}),p.pendingRequestCount>0?(t(),a(L,{key:0,class:"badge"},{default:s(()=>[d(r(p.pendingRequestCount>99?"99+":p.pendingRequestCount),1)]),_:1})):o("v-if",!0)]),_:1})]),_:1},8,["onClick"]),i(y,{class:"add-menu-item",onClick:M.logout},{default:s(()=>[i(y,{class:"menu-item-content"},{default:s(()=>[i(L,{class:"add-menu-icon"},{default:s(()=>[d("🚪")]),_:1}),i(L,{class:"add-menu-text"},{default:s(()=>[d("退出登录")]),_:1})]),_:1})]),_:1},8,["onClick"])]),_:1})]),_:1},8,["onClick"])):o("v-if",!0),o(" 自定义底部导航栏 "),i(I,{"current-page":"chat"})]),_:1})}],["__scopeId","data-v-151731cc"]]);export{L as default};
