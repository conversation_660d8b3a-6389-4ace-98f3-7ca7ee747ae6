import{_ as e,o as s,c as t,w as o,i as a,a as i,b as n,d as l,t as c,e as r,r as u,F as d,n as h,k as m,f,g,S as w,l as _}from"./index-eb56c588.js";import{b as p,m as k,s as M}from"./api.2bc223ab.js";import{a as T,c as I,s as C}from"./websocket.b01ea1d9.js";const y=e({data:()=>({userId:null,nickname:"",userInfo:null,messages:[],inputMessage:"",scrollTop:0,sending:!1,showEmojiPanel:!1,showMorePanel:!1,showChatMenuModal:!1,hasMoreMessages:!1,loadingMore:!1,currentPage:1,pageSize:20,lastMessageTime:null,isConnected:!1,messageListHeight:600,isRecordingVoice:!1,scrollTimer:null,emojiList:["😀","😃","😄","😁","😆","😅","😂","🤣","😊","😇","🙂","🙃","😉","😌","😍","🥰","😘","😗","😙","😚","😋","😛","😝","😜","🤪","🤨","🧐","🤓","😎","🤩","🥳","😏","😒","😞","😔","😟","😕","🙁","☹️","😣","😖","😫","😩","🥺","😢","😭","😤","😠","😡","🤬","🤯","😳","🥵","🥶","😱","😨","😰","😥","😓","🤗","🤔","🤭","🤫","🤥","😶","😐","😑","😬","🙄","😯","😦","😧","😮","😲","🥱","😴","🤤","😪","😵","🤐","🥴","🤢","🤮","🤧","😷","🤒","🤕","🤑","🤠","😈","👿","👹","👺","🤡","💩","👻","💀","☠️","👽","👾","🤖","🎃","😺","😸","😹","😻","😼","😽","🙀","😿","😾","👋","🤚","🖐️","✋","🖖","👌","🤏","✌️","🤞","🤟","🤘","🤙","👈","👉","👆","🖕","👇","☝️","👍","👎","👊","✊","🤛","🤜","👏","🙌","👐","🤲","🤝","🙏","✍️","💅","🤳","❤️","🧡","💛","💚","💙","💜","🖤","🤍","🤎","💔","❣️","💕","💞","💓","💗","💖","💘","💝","💟","☮️","✝️","☪️","🕉️","☸️","✡️","🔯","🕎","☯️","☦️","🛐","⛎","♈","♉","♊","♋","♌","♍","♎","♏","♐","♑","♒","♓","🆔","⚛️","🉑","☢️","☣️"]}),computed:{computedMessageListHeight(){var e;try{const s=uni.getSystemInfoSync(),t=s.statusBarHeight||0,o=88,a=this.showEmojiPanel||this.showMorePanel?200:50,i=(null==(e=s.safeAreaInsets)?void 0:e.bottom)||0,n=s.windowHeight-t-o-a-i;return Math.max(n,300)}catch(s){return console.error("计算高度失败:",s),600}}},onLoad(e){if(this.userId=parseInt(e.userId),this.nickname=decodeURIComponent(e.nickname||"未知用户"),this.userInfo=uni.getStorageSync("userInfo"),console.log("聊天详情页面参数:",{userId:this.userId,nickname:this.nickname,userInfo:this.userInfo}),!this.userInfo||!uni.getStorageSync("token"))return console.log("用户未登录，跳转到登录页面"),void uni.reLaunch({url:"/pages/login/login"});uni.setNavigationBarTitle({title:this.nickname}),this.calculateMessageListHeight(),this.loadChatHistory(),this.initWebSocket()},onUnload(){T(),this.scrollTimer&&clearTimeout(this.scrollTimer)},onReady(){this.calculateMessageListHeight()},methods:{async loadChatHistory(){try{const e=await p(this.userId);if(200===e.code){const s=e.data||[];this.messages=s.sort((e,s)=>new Date(e.sendTime)-new Date(s.sendTime)),this.scrollToBottom(),this.markAsRead()}else console.error("加载聊天记录失败:",e.message),uni.showToast({title:e.message||"加载失败",icon:"none"})}catch(e){console.error("加载聊天记录失败:",e),uni.showToast({title:"网络错误",icon:"none"})}},async loadMoreMessages(){},initWebSocket(){const e=uni.getStorageSync("token");if(!e)return console.error("未找到token，无法连接WebSocket"),void(this.isConnected=!0);try{I(e,e=>{if("chat"===e.type){if(e.fromUserId!==this.userInfo.userId){this.messages.find(s=>s.id===e.messageId||s.content===e.content&&s.fromUserId===e.fromUserId&&Math.abs(new Date(s.sendTime)-new Date(e.sendTime))<1e3)||(this.messages.push({id:e.messageId||Date.now(),fromUserId:e.fromUserId,toUserId:e.toUserId,content:e.content,sendTime:e.sendTime||(new Date).toISOString()}),this.scrollToBottom(),this.markAsRead())}}else"auth_success"===e.type&&(this.isConnected=!0)}),this.isConnected=!0}catch(s){console.error("WebSocket连接失败:",s),this.isConnected=!0}},reconnectWebSocket(){this.isConnected=!1,uni.showLoading({title:"连接中..."}),setTimeout(()=>{this.initWebSocket(),uni.hideLoading()},1e3)},async sendMessage(){if(!this.inputMessage.trim()||this.sending)return;const e=this.inputMessage.trim(),s=Date.now(),t={id:s,fromUserId:this.userInfo.userId,toUserId:this.userId,content:e,sendTime:(new Date).toISOString(),status:"sending"};this.messages.push(t),this.inputMessage="",this.sending=!0,this.hideEmojiPanel(),this.hideMorePanel(),this.scrollToBottom();try{const t=await this.sendMessageAPI({toUserId:this.userId,content:e});if(200!==t.code)throw new Error(t.message||"发送失败");{const e=this.messages.findIndex(e=>e.id===s);e>-1&&(this.messages[e].id=t.data.id,this.messages[e].status="sent",this.messages[e].sendTime=t.data.sendTime)}}catch(o){console.error("发送消息失败:",o);const e=this.messages.findIndex(e=>e.id===s);e>-1&&(this.messages[e].status="failed"),uni.showToast({title:o.message||"发送失败",icon:"none"})}finally{this.sending=!1}},formatTime(e){if(!e)return"";return new Date(e).toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit",hour12:!1})},formatDetailTime(e){if(!e)return"";const s=new Date(e),t=new Date,o=new Date(t.getFullYear(),t.getMonth(),t.getDate()),a=new Date(o.getTime()-864e5);return s>=o?"今天 "+s.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit",hour12:!1}):s>=a?"昨天 "+s.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit",hour12:!1}):s.toLocaleDateString("zh-CN")+" "+s.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit",hour12:!1})},shouldShowTime(e){const s=this.messages.findIndex(s=>s.id===e.id);if(0===s)return!0;const t=this.messages[s-1];if(!t)return!0;return new Date(e.sendTime)-new Date(t.sendTime)>6e5},scrollToBottom(){this.scrollTimer&&clearTimeout(this.scrollTimer),this.scrollTimer=setTimeout(()=>{this.$nextTick(()=>{this.scrollTop=0,this.$nextTick(()=>{this.scrollTop=999999})})},50)},async markAsRead(){try{await k(this.userId),uni.$emit("clearUnreadCount",{userId:this.userId})}catch(e){console.error("标记会话已读失败:",e)}},toggleEmojiPanel(){this.showEmojiPanel=!this.showEmojiPanel},hideEmojiPanel(){this.showEmojiPanel=!1},hideMorePanel(){this.showMorePanel=!1},sendMessageAPI:async e=>await M(e),insertEmoji(e){this.inputMessage+=e},goBack(){uni.navigateBack()},showChatMenu(){this.showChatMenuModal=!0},hideChatMenu(){this.showChatMenuModal=!1},calculateMessageListHeight(){this.$forceUpdate()},onInputFocus(){this.showEmojiPanel=!1,this.showMorePanel=!1,this.calculateMessageListHeight(),this.sendTypingStatus(!0)},onInputBlur(){this.calculateMessageListHeight(),this.sendTypingStatus(!1)},sendTypingStatus(e){try{const s={type:"typing",toUserId:this.userId,fromUserId:this.userInfo.userId,isTyping:e};C(s)}catch(s){console.log("发送输入状态失败:",s)}},toggleMorePanel(){this.showMorePanel=!this.showMorePanel,this.showEmojiPanel=!1,this.calculateMessageListHeight()},toggleEmojiPanel(){this.showEmojiPanel=!this.showEmojiPanel,this.showMorePanel=!1,this.calculateMessageListHeight()},startVoiceRecord(){this.isRecordingVoice=!0,uni.showToast({title:"开始录音",icon:"none"})},endVoiceRecord(){this.isRecordingVoice=!1,uni.showToast({title:"录音结束",icon:"none"})},chooseImage(){this.showMorePanel=!1,uni.chooseImage({count:1,success:e=>{uni.showToast({title:"图片功能开发中",icon:"none"})}})},takePhoto(){this.showMorePanel=!1,uni.showToast({title:"拍照功能开发中",icon:"none"})},chooseVideo(){this.showMorePanel=!1,uni.showToast({title:"视频功能开发中",icon:"none"})},chooseFile(){this.showMorePanel=!1,uni.showToast({title:"文件功能开发中",icon:"none"})},shareLocation(){this.showMorePanel=!1,uni.showToast({title:"位置功能开发中",icon:"none"})},sendVoice(){this.showMorePanel=!1,uni.showToast({title:"语音功能开发中",icon:"none"})},sendRedPacket(){this.showMorePanel=!1,uni.showToast({title:"红包功能开发中",icon:"none"})},transfer(){this.showMorePanel=!1,uni.showToast({title:"转账功能开发中",icon:"none"})},viewProfile(){this.hideChatMenu(),uni.showToast({title:"查看资料功能开发中",icon:"none"})},clearHistory(){this.hideChatMenu(),uni.showModal({title:"确认清空",content:"确定要清空所有聊天记录吗？",success:e=>{e.confirm&&(this.messages=[],uni.showToast({title:"已清空聊天记录",icon:"success"}))}})},setBackground(){this.hideChatMenu(),uni.showToast({title:"设置背景功能开发中",icon:"none"})}}},[["render",function(e,p,k,M,T,I){const C=g,y=a,P=w,b=_;return s(),t(y,{class:"chat-detail"},{default:o(()=>[i(" 顶部导航栏 "),n(y,{class:"chat-header"},{default:o(()=>[n(y,{class:"header-content"},{default:o(()=>[n(y,{class:"header-left",onClick:I.goBack},{default:o(()=>[n(C,{class:"back-icon"},{default:o(()=>[l("‹")]),_:1})]),_:1},8,["onClick"]),n(y,{class:"header-center"},{default:o(()=>[n(C,{class:"chat-title"},{default:o(()=>[l(c(T.nickname),1)]),_:1}),n(C,{class:"online-status"},{default:o(()=>[l(c(T.isConnected?"在线":"离线"),1)]),_:1})]),_:1}),n(y,{class:"header-right"},{default:o(()=>[n(y,{class:"more-icon",onClick:I.showChatMenu},{default:o(()=>[n(C,null,{default:o(()=>[l("⋯")]),_:1})]),_:1},8,["onClick"])]),_:1})]),_:1})]),_:1}),i(" 连接状态提示 - 已隐藏，改为静默重连 "),i(" 消息列表 "),n(P,{class:"message-list","scroll-y":"","scroll-top":T.scrollTop,"scroll-with-animation":!0,onScrolltoupper:I.loadMoreMessages,"enable-back-to-top":!1,enhanced:!0,bounces:!1,"show-scrollbar":!1,style:h({height:I.computedMessageListHeight+"px"})},{default:o(()=>[i(" 背景图片 "),n(y,{class:"chat-background"}),i(" 加载更多提示 "),T.hasMoreMessages?(s(),t(y,{key:0,class:"load-more"},{default:o(()=>[n(y,{class:"loading-indicator"},{default:o(()=>[n(C,{class:"loading-text"},{default:o(()=>[l(c(T.loadingMore?"加载中...":"下拉加载更多"),1)]),_:1})]),_:1})]),_:1})):i("v-if",!0),i(" 消息列表 "),(s(!0),r(d,null,u(T.messages,e=>(s(),t(y,{key:e.id,class:m(["message-item",{"own-message":e.fromUserId===T.userInfo.userId}])},{default:o(()=>[i(" 时间分割线 "),I.shouldShowTime(e)?(s(),t(y,{key:0,class:"time-divider"},{default:o(()=>[n(y,{class:"time-label"},{default:o(()=>[n(C,{class:"time-text"},{default:o(()=>[l(c(I.formatDetailTime(e.sendTime)),1)]),_:2},1024)]),_:2},1024)]),_:2},1024)):i("v-if",!0),i(" 消息内容 "),n(y,{class:m(["message-wrapper",{"own-message-wrapper":e.fromUserId===T.userInfo.userId}])},{default:o(()=>[i(" 消息气泡 "),n(y,{class:m(["message-bubble",{"own-bubble":e.fromUserId===T.userInfo.userId}])},{default:o(()=>[i(" 消息内容 "),n(y,{class:"message-content"},{default:o(()=>[n(C,{class:"message-text"},{default:o(()=>[l(c(e.content),1)]),_:2},1024)]),_:2},1024)]),_:2},1032,["class"])]),_:2},1032,["class"]),i(" 消息状态和时间 "),e.fromUserId===T.userInfo.userId?(s(),t(y,{key:1,class:"message-status own-status"},{default:o(()=>[n(C,{class:"message-time"},{default:o(()=>[l(c(I.formatTime(e.sendTime)),1)]),_:2},1024),n(y,{class:"send-status"},{default:o(()=>["sending"===e.status?(s(),t(C,{key:0,class:"status-sending"},{default:o(()=>[l("发送中")]),_:1})):"failed"===e.status?(s(),t(C,{key:1,class:"status-failed"},{default:o(()=>[l("发送失败")]),_:1})):(s(),t(C,{key:2,class:"status-sent"},{default:o(()=>[l("✓")]),_:1}))]),_:2},1024)]),_:2},1024)):i("v-if",!0)]),_:2},1032,["class"]))),128)),i(" 空状态 "),0===T.messages.length?(s(),t(y,{key:1,class:"empty-messages"},{default:o(()=>[n(y,{class:"empty-icon"},{default:o(()=>[l("💬")]),_:1}),n(C,{class:"empty-text"},{default:o(()=>[l("暂无聊天记录")]),_:1}),n(C,{class:"empty-tip"},{default:o(()=>[l("开始你们的第一次对话吧")]),_:1})]),_:1})):i("v-if",!0)]),_:1},8,["scroll-top","onScrolltoupper","style"]),i(" 输入区域 "),n(y,{class:m(["input-area",{"panel-open":T.showEmojiPanel||T.showMorePanel}])},{default:o(()=>[n(y,{class:"input-wrapper"},{default:o(()=>[i(" 语音/更多功能按钮 "),n(y,{class:m(["function-btn",{active:T.showMorePanel}]),onClick:I.toggleMorePanel},{default:o(()=>[n(C,{class:"function-icon"},{default:o(()=>[l(c(T.showMorePanel?"⌨️":"+"),1)]),_:1})]),_:1},8,["onClick","class"]),i(" 输入框容器 "),n(y,{class:"input-container"},{default:o(()=>[i(" 输入框 "),n(b,{modelValue:T.inputMessage,"onUpdate:modelValue":p[0]||(p[0]=e=>T.inputMessage=e),placeholder:"输入消息...",class:"message-input","auto-height":!0,maxlength:500,onConfirm:I.sendMessage,onFocus:I.onInputFocus,onBlur:I.onInputBlur,"adjust-position":!1,"show-confirm-bar":!1},null,8,["modelValue","onConfirm","onFocus","onBlur"])]),_:1}),i(" 表情按钮 "),n(y,{class:m(["function-btn",{active:T.showEmojiPanel}]),onClick:I.toggleEmojiPanel},{default:o(()=>[n(C,{class:"function-icon"},{default:o(()=>[l("😊")]),_:1})]),_:1},8,["onClick","class"]),i(" 发送按钮 "),T.inputMessage.trim()?(s(),t(y,{key:0,class:m(["send-btn",{sending:T.sending}]),onClick:I.sendMessage},{default:o(()=>[n(C,{class:"send-text"},{default:o(()=>[l(c(T.sending?"...":"发送"),1)]),_:1})]),_:1},8,["onClick","class"])):(s(),r(d,{key:1},[i(" 语音按钮 "),n(y,{class:"voice-btn",onTouchstart:I.startVoiceRecord,onTouchend:I.endVoiceRecord},{default:o(()=>[n(C,{class:"voice-icon"},{default:o(()=>[l("🎤")]),_:1})]),_:1},8,["onTouchstart","onTouchend"])],2112))]),_:1}),i(" 表情面板 "),T.showEmojiPanel?(s(),t(y,{key:0,class:"emoji-panel"},{default:o(()=>[n(y,{class:"emoji-header"},{default:o(()=>[n(C,{class:"emoji-title"},{default:o(()=>[l("表情")]),_:1})]),_:1}),n(P,{class:"emoji-scroll","scroll-y":""},{default:o(()=>[n(y,{class:"emoji-grid"},{default:o(()=>[(s(!0),r(d,null,u(T.emojiList,e=>(s(),t(y,{key:e,class:"emoji-item",onClick:s=>I.insertEmoji(e)},{default:o(()=>[n(C,{class:"emoji-text"},{default:o(()=>[l(c(e),1)]),_:2},1024)]),_:2},1032,["onClick"]))),128))]),_:1})]),_:1})]),_:1})):i("v-if",!0),i(" 更多功能面板 "),T.showMorePanel?(s(),t(y,{key:1,class:"more-panel"},{default:o(()=>[n(y,{class:"more-header"},{default:o(()=>[n(C,{class:"more-title"},{default:o(()=>[l("更多功能")]),_:1})]),_:1}),n(y,{class:"more-grid"},{default:o(()=>[n(y,{class:"more-item",onClick:I.chooseImage},{default:o(()=>[n(y,{class:"more-icon-wrapper"},{default:o(()=>[n(C,{class:"more-icon"},{default:o(()=>[l("📷")]),_:1})]),_:1}),n(C,{class:"more-text"},{default:o(()=>[l("相册")]),_:1})]),_:1},8,["onClick"]),n(y,{class:"more-item",onClick:I.takePhoto},{default:o(()=>[n(y,{class:"more-icon-wrapper"},{default:o(()=>[n(C,{class:"more-icon"},{default:o(()=>[l("📸")]),_:1})]),_:1}),n(C,{class:"more-text"},{default:o(()=>[l("拍照")]),_:1})]),_:1},8,["onClick"]),n(y,{class:"more-item",onClick:I.chooseVideo},{default:o(()=>[n(y,{class:"more-icon-wrapper"},{default:o(()=>[n(C,{class:"more-icon"},{default:o(()=>[l("🎥")]),_:1})]),_:1}),n(C,{class:"more-text"},{default:o(()=>[l("视频")]),_:1})]),_:1},8,["onClick"]),n(y,{class:"more-item",onClick:I.chooseFile},{default:o(()=>[n(y,{class:"more-icon-wrapper"},{default:o(()=>[n(C,{class:"more-icon"},{default:o(()=>[l("📁")]),_:1})]),_:1}),n(C,{class:"more-text"},{default:o(()=>[l("文件")]),_:1})]),_:1},8,["onClick"]),n(y,{class:"more-item",onClick:I.shareLocation},{default:o(()=>[n(y,{class:"more-icon-wrapper"},{default:o(()=>[n(C,{class:"more-icon"},{default:o(()=>[l("📍")]),_:1})]),_:1}),n(C,{class:"more-text"},{default:o(()=>[l("位置")]),_:1})]),_:1},8,["onClick"]),n(y,{class:"more-item",onClick:I.sendVoice},{default:o(()=>[n(y,{class:"more-icon-wrapper"},{default:o(()=>[n(C,{class:"more-icon"},{default:o(()=>[l("🎤")]),_:1})]),_:1}),n(C,{class:"more-text"},{default:o(()=>[l("语音")]),_:1})]),_:1},8,["onClick"]),n(y,{class:"more-item",onClick:I.sendRedPacket},{default:o(()=>[n(y,{class:"more-icon-wrapper"},{default:o(()=>[n(C,{class:"more-icon"},{default:o(()=>[l("🧧")]),_:1})]),_:1}),n(C,{class:"more-text"},{default:o(()=>[l("红包")]),_:1})]),_:1},8,["onClick"]),n(y,{class:"more-item",onClick:I.transfer},{default:o(()=>[n(y,{class:"more-icon-wrapper"},{default:o(()=>[n(C,{class:"more-icon"},{default:o(()=>[l("💰")]),_:1})]),_:1}),n(C,{class:"more-text"},{default:o(()=>[l("转账")]),_:1})]),_:1},8,["onClick"])]),_:1})]),_:1})):i("v-if",!0)]),_:1},8,["class"]),i(" 聊天菜单弹窗 "),T.showChatMenuModal?(s(),t(y,{key:0,class:"chat-menu-modal",onClick:I.hideChatMenu},{default:o(()=>[n(y,{class:"chat-menu",onClick:p[1]||(p[1]=f(()=>{},["stop"]))},{default:o(()=>[n(y,{class:"chat-menu-item",onClick:I.viewProfile},{default:o(()=>[n(C,{class:"menu-icon"},{default:o(()=>[l("👤")]),_:1}),n(C,{class:"menu-text"},{default:o(()=>[l("查看资料")]),_:1})]),_:1},8,["onClick"]),n(y,{class:"chat-menu-item",onClick:I.clearHistory},{default:o(()=>[n(C,{class:"menu-icon"},{default:o(()=>[l("🗑️")]),_:1}),n(C,{class:"menu-text"},{default:o(()=>[l("清空聊天记录")]),_:1})]),_:1},8,["onClick"]),n(y,{class:"chat-menu-item",onClick:I.setBackground},{default:o(()=>[n(C,{class:"menu-icon"},{default:o(()=>[l("🖼️")]),_:1}),n(C,{class:"menu-text"},{default:o(()=>[l("设置背景")]),_:1})]),_:1},8,["onClick"])]),_:1})]),_:1},8,["onClick"])):i("v-if",!0)]),_:1})}],["__scopeId","data-v-b79f7bb6"]]);export{y as default};
