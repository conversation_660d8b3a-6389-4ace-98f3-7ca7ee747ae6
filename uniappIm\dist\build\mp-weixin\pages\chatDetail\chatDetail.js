"use strict";const e=require("../../common/vendor.js"),s=require("../../utils/api.js"),o=require("../../utils/websocket.js");require("../../utils/config.js");const t={data:()=>({userId:null,nickname:"",userInfo:null,messages:[],inputMessage:"",scrollTop:0,sending:!1,showEmojiPanel:!1,showMorePanel:!1,showChatMenuModal:!1,hasMoreMessages:!1,loadingMore:!1,currentPage:1,pageSize:20,lastMessageTime:null,isConnected:!1,messageListHeight:600,isRecordingVoice:!1,scrollTimer:null,emojiList:["😀","😃","😄","😁","😆","😅","😂","🤣","😊","😇","🙂","🙃","😉","😌","😍","🥰","😘","😗","😙","😚","😋","😛","😝","😜","🤪","🤨","🧐","🤓","😎","🤩","🥳","😏","😒","😞","😔","😟","😕","🙁","☹️","😣","😖","😫","😩","🥺","😢","😭","😤","😠","😡","🤬","🤯","😳","🥵","🥶","😱","😨","😰","😥","😓","🤗","🤔","🤭","🤫","🤥","😶","😐","😑","😬","🙄","😯","😦","😧","😮","😲","🥱","😴","🤤","😪","😵","🤐","🥴","🤢","🤮","🤧","😷","🤒","🤕","🤑","🤠","😈","👿","👹","👺","🤡","💩","👻","💀","☠️","👽","👾","🤖","🎃","😺","😸","😹","😻","😼","😽","🙀","😿","😾","👋","🤚","🖐️","✋","🖖","👌","🤏","✌️","🤞","🤟","🤘","🤙","👈","👉","👆","🖕","👇","☝️","👍","👎","👊","✊","🤛","🤜","👏","🙌","👐","🤲","🤝","🙏","✍️","💅","🤳","❤️","🧡","💛","💚","💙","💜","🖤","🤍","🤎","💔","❣️","💕","💞","💓","💗","💖","💘","💝","💟","☮️","✝️","☪️","🕉️","☸️","✡️","🔯","🕎","☯️","☦️","🛐","⛎","♈","♉","♊","♋","♌","♍","♎","♏","♐","♑","♒","♓","🆔","⚛️","🉑","☢️","☣️"]}),computed:{computedMessageListHeight(){var s;try{const o=e.index.getSystemInfoSync(),t=o.statusBarHeight||0,i=88,n=this.showEmojiPanel||this.showMorePanel?200:50,a=(null==(s=o.safeAreaInsets)?void 0:s.bottom)||0,r=o.windowHeight-t-i-n-a;return Math.max(r,300)}catch(o){return console.error("计算高度失败:",o),600}}},onLoad(s){if(this.userId=parseInt(s.userId),this.nickname=decodeURIComponent(s.nickname||"未知用户"),this.userInfo=e.index.getStorageSync("userInfo"),console.log("聊天详情页面参数:",{userId:this.userId,nickname:this.nickname,userInfo:this.userInfo}),!this.userInfo||!e.index.getStorageSync("token"))return console.log("用户未登录，跳转到登录页面"),void e.index.reLaunch({url:"/pages/login/login"});e.index.setNavigationBarTitle({title:this.nickname}),this.calculateMessageListHeight(),this.loadChatHistory(),this.initWebSocket()},onUnload(){o.closeWebSocket(),this.scrollTimer&&clearTimeout(this.scrollTimer)},onReady(){this.calculateMessageListHeight()},methods:{async loadChatHistory(){try{const o=await s.getChatHistory(this.userId);if(200===o.code){const e=o.data||[];this.messages=e.sort((e,s)=>new Date(e.sendTime)-new Date(s.sendTime)),this.scrollToBottom(),this.markAsRead()}else console.error("加载聊天记录失败:",o.message),e.index.showToast({title:o.message||"加载失败",icon:"none"})}catch(o){console.error("加载聊天记录失败:",o),e.index.showToast({title:"网络错误",icon:"none"})}},async loadMoreMessages(){},initWebSocket(){const s=e.index.getStorageSync("token");if(!s)return console.error("未找到token，无法连接WebSocket"),void(this.isConnected=!0);try{o.connectWebSocket(s,e=>{if("chat"===e.type){if(e.fromUserId!==this.userInfo.userId){this.messages.find(s=>s.id===e.messageId||s.content===e.content&&s.fromUserId===e.fromUserId&&Math.abs(new Date(s.sendTime)-new Date(e.sendTime))<1e3)||(this.messages.push({id:e.messageId||Date.now(),fromUserId:e.fromUserId,toUserId:e.toUserId,content:e.content,sendTime:e.sendTime||(new Date).toISOString()}),this.scrollToBottom(),this.markAsRead())}}else"auth_success"===e.type&&(this.isConnected=!0)}),this.isConnected=!0}catch(t){console.error("WebSocket连接失败:",t),this.isConnected=!0}},reconnectWebSocket(){this.isConnected=!1,e.index.showLoading({title:"连接中..."}),setTimeout(()=>{this.initWebSocket(),e.index.hideLoading()},1e3)},async sendMessage(){if(!this.inputMessage.trim()||this.sending)return;const s=this.inputMessage.trim(),o=Date.now(),t={id:o,fromUserId:this.userInfo.userId,toUserId:this.userId,content:s,sendTime:(new Date).toISOString(),status:"sending"};this.messages.push(t),this.inputMessage="",this.sending=!0,this.hideEmojiPanel(),this.hideMorePanel(),this.scrollToBottom();try{const e=await this.sendMessageAPI({toUserId:this.userId,content:s});if(200!==e.code)throw new Error(e.message||"发送失败");{const s=this.messages.findIndex(e=>e.id===o);s>-1&&(this.messages[s].id=e.data.id,this.messages[s].status="sent",this.messages[s].sendTime=e.data.sendTime)}}catch(i){console.error("发送消息失败:",i);const s=this.messages.findIndex(e=>e.id===o);s>-1&&(this.messages[s].status="failed"),e.index.showToast({title:i.message||"发送失败",icon:"none"})}finally{this.sending=!1}},formatTime(e){if(!e)return"";return new Date(e).toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit",hour12:!1})},formatDetailTime(e){if(!e)return"";const s=new Date(e),o=new Date,t=new Date(o.getFullYear(),o.getMonth(),o.getDate()),i=new Date(t.getTime()-864e5);return s>=t?"今天 "+s.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit",hour12:!1}):s>=i?"昨天 "+s.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit",hour12:!1}):s.toLocaleDateString("zh-CN")+" "+s.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit",hour12:!1})},shouldShowTime(e){const s=this.messages.findIndex(s=>s.id===e.id);if(0===s)return!0;const o=this.messages[s-1];if(!o)return!0;return new Date(e.sendTime)-new Date(o.sendTime)>6e5},scrollToBottom(){this.scrollTimer&&clearTimeout(this.scrollTimer),this.scrollTimer=setTimeout(()=>{this.$nextTick(()=>{this.scrollTop=0,this.$nextTick(()=>{this.scrollTop=999999})})},50)},async markAsRead(){try{await s.markSessionAsRead(this.userId),e.index.$emit("clearUnreadCount",{userId:this.userId})}catch(o){console.error("标记会话已读失败:",o)}},toggleEmojiPanel(){this.showEmojiPanel=!this.showEmojiPanel},hideEmojiPanel(){this.showEmojiPanel=!1},hideMorePanel(){this.showMorePanel=!1},sendMessageAPI:async e=>await s.sendMessage(e),insertEmoji(e){this.inputMessage+=e},goBack(){e.index.navigateBack()},showChatMenu(){this.showChatMenuModal=!0},hideChatMenu(){this.showChatMenuModal=!1},calculateMessageListHeight(){this.$forceUpdate()},onInputFocus(){this.showEmojiPanel=!1,this.showMorePanel=!1,this.calculateMessageListHeight(),this.sendTypingStatus(!0)},onInputBlur(){this.calculateMessageListHeight(),this.sendTypingStatus(!1)},sendTypingStatus(e){try{const s={type:"typing",toUserId:this.userId,fromUserId:this.userInfo.userId,isTyping:e};o.sendWebSocketMessage(s)}catch(s){console.log("发送输入状态失败:",s)}},toggleMorePanel(){this.showMorePanel=!this.showMorePanel,this.showEmojiPanel=!1,this.calculateMessageListHeight()},toggleEmojiPanel(){this.showEmojiPanel=!this.showEmojiPanel,this.showMorePanel=!1,this.calculateMessageListHeight()},startVoiceRecord(){this.isRecordingVoice=!0,e.index.showToast({title:"开始录音",icon:"none"})},endVoiceRecord(){this.isRecordingVoice=!1,e.index.showToast({title:"录音结束",icon:"none"})},chooseImage(){this.showMorePanel=!1,e.index.chooseImage({count:1,success:s=>{e.index.showToast({title:"图片功能开发中",icon:"none"})}})},takePhoto(){this.showMorePanel=!1,e.index.showToast({title:"拍照功能开发中",icon:"none"})},chooseVideo(){this.showMorePanel=!1,e.index.showToast({title:"视频功能开发中",icon:"none"})},chooseFile(){this.showMorePanel=!1,e.index.showToast({title:"文件功能开发中",icon:"none"})},shareLocation(){this.showMorePanel=!1,e.index.showToast({title:"位置功能开发中",icon:"none"})},sendVoice(){this.showMorePanel=!1,e.index.showToast({title:"语音功能开发中",icon:"none"})},sendRedPacket(){this.showMorePanel=!1,e.index.showToast({title:"红包功能开发中",icon:"none"})},transfer(){this.showMorePanel=!1,e.index.showToast({title:"转账功能开发中",icon:"none"})},viewProfile(){this.hideChatMenu(),e.index.showToast({title:"查看资料功能开发中",icon:"none"})},clearHistory(){this.hideChatMenu(),e.index.showModal({title:"确认清空",content:"确定要清空所有聊天记录吗？",success:s=>{s.confirm&&(this.messages=[],e.index.showToast({title:"已清空聊天记录",icon:"success"}))}})},setBackground(){this.hideChatMenu(),e.index.showToast({title:"设置背景功能开发中",icon:"none"})}}};const i=e._export_sfc(t,[["render",function(s,o,t,i,n,a){return e.e({a:e.o((...e)=>a.goBack&&a.goBack(...e)),b:e.t(n.nickname),c:e.t(n.isConnected?"在线":"离线"),d:e.o((...e)=>a.showChatMenu&&a.showChatMenu(...e)),e:n.hasMoreMessages},n.hasMoreMessages?{f:e.t(n.loadingMore?"加载中...":"下拉加载更多")}:{},{g:e.f(n.messages,(s,o,t)=>e.e({a:a.shouldShowTime(s)},a.shouldShowTime(s)?{b:e.t(a.formatDetailTime(s.sendTime))}:{},{c:e.t(s.content),d:s.fromUserId===n.userInfo.userId?1:"",e:s.fromUserId===n.userInfo.userId?1:"",f:s.fromUserId===n.userInfo.userId},s.fromUserId===n.userInfo.userId?e.e({g:e.t(a.formatTime(s.sendTime)),h:"sending"===s.status},("sending"===s.status||s.status,{}),{i:"failed"===s.status}):{},{j:s.id,k:s.fromUserId===n.userInfo.userId?1:""})),h:0===n.messages.length},(n.messages.length,{}),{i:n.scrollTop,j:e.o((...e)=>a.loadMoreMessages&&a.loadMoreMessages(...e)),k:a.computedMessageListHeight+"px",l:e.t(n.showMorePanel?"⌨️":"+"),m:e.o((...e)=>a.toggleMorePanel&&a.toggleMorePanel(...e)),n:n.showMorePanel?1:"",o:e.o((...e)=>a.sendMessage&&a.sendMessage(...e)),p:e.o((...e)=>a.onInputFocus&&a.onInputFocus(...e)),q:e.o((...e)=>a.onInputBlur&&a.onInputBlur(...e)),r:n.inputMessage,s:e.o(e=>n.inputMessage=e.detail.value),t:e.o((...e)=>a.toggleEmojiPanel&&a.toggleEmojiPanel(...e)),v:n.showEmojiPanel?1:"",w:n.inputMessage.trim()},n.inputMessage.trim()?{x:e.t(n.sending?"...":"发送"),y:e.o((...e)=>a.sendMessage&&a.sendMessage(...e)),z:n.sending?1:""}:{A:e.o((...e)=>a.startVoiceRecord&&a.startVoiceRecord(...e)),B:e.o((...e)=>a.endVoiceRecord&&a.endVoiceRecord(...e))},{C:n.showEmojiPanel},n.showEmojiPanel?{D:e.f(n.emojiList,(s,o,t)=>({a:e.t(s),b:s,c:e.o(e=>a.insertEmoji(s),s)}))}:{},{E:n.showMorePanel},n.showMorePanel?{F:e.o((...e)=>a.chooseImage&&a.chooseImage(...e)),G:e.o((...e)=>a.takePhoto&&a.takePhoto(...e)),H:e.o((...e)=>a.chooseVideo&&a.chooseVideo(...e)),I:e.o((...e)=>a.chooseFile&&a.chooseFile(...e)),J:e.o((...e)=>a.shareLocation&&a.shareLocation(...e)),K:e.o((...e)=>a.sendVoice&&a.sendVoice(...e)),L:e.o((...e)=>a.sendRedPacket&&a.sendRedPacket(...e)),M:e.o((...e)=>a.transfer&&a.transfer(...e))}:{},{N:n.showEmojiPanel||n.showMorePanel?1:"",O:n.showChatMenuModal},n.showChatMenuModal?{P:e.o((...e)=>a.viewProfile&&a.viewProfile(...e)),Q:e.o((...e)=>a.clearHistory&&a.clearHistory(...e)),R:e.o((...e)=>a.setBackground&&a.setBackground(...e)),S:e.o(()=>{}),T:e.o((...e)=>a.hideChatMenu&&a.hideChatMenu(...e))}:{})}],["__scopeId","data-v-b79f7bb6"]]);wx.createPage(i);
