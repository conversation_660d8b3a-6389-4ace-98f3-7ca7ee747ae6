import{_ as e,o as t,c as s,w as a,i as n,a as o,b as l,d as c,e as i,r,F as u,g as d,t as f,k as g,h}from"./index-eb56c588.js";import{f as m,h as _}from"./api.2bc223ab.js";const T=e({data:()=>({friendRequests:[],loading:!1}),onLoad(){this.loadFriendRequests()},onShow(){this.loadFriendRequests()},methods:{goBack(){uni.navigateBack()},async loadFriendRequests(){this.loading=!0;try{const e=uni.getStorageSync("userInfo");if(!e)return void uni.showToast({title:"请先登录",icon:"none"});const t=await m(e.userId);console.log("好友申请列表:",t),200===t.code?this.friendRequests=t.data:uni.showToast({title:t.message||"加载失败",icon:"none"})}catch(e){console.error("加载好友申请失败:",e),uni.showToast({title:"网络错误，请重试",icon:"none"})}finally{this.loading=!1}},async handleRequest(e,t){try{const s=uni.getStorageSync("userInfo");if(!s)return void uni.showToast({title:"请先登录",icon:"none"});const a=await _({requestId:e.id,action:t,userId:s.userId});console.log("处理好友申请结果:",a),200===a.code?(e.status=t,this.$forceUpdate(),uni.showToast({title:"ACCEPTED"===t?"已同意好友申请":"已拒绝好友申请",icon:"success"}),"ACCEPTED"===t&&uni.$emit("friendListUpdate")):uni.showToast({title:a.message||"操作失败",icon:"none"})}catch(s){console.error("处理好友申请失败:",s),uni.showToast({title:"网络错误，请重试",icon:"none"})}},getAvatarText:e=>e.nickname?e.nickname.charAt(0):e.username.charAt(0),formatTime(e){if(!e)return"";const t=new Date(e),s=new Date-t;return s<6e4?"刚刚":s<36e5?Math.floor(s/6e4)+"分钟前":s<864e5?Math.floor(s/36e5)+"小时前":t.toLocaleDateString()},getStatusText(e){switch(e){case"ACCEPTED":return"已同意";case"REJECTED":return"已拒绝";case"PENDING":return"待处理";default:return"未知状态"}},getStatusClass(e){switch(e){case"ACCEPTED":return"status-accepted";case"REJECTED":return"status-rejected";default:return"status-pending"}}}},[["render",function(e,m,_,T,C,E){const k=d,q=n,y=h;return t(),s(q,{class:"container"},{default:a(()=>[o(" 自定义导航栏 "),l(q,{class:"custom-navbar"},{default:a(()=>[l(q,{class:"navbar-content"},{default:a(()=>[l(q,{class:"navbar-left",onClick:E.goBack},{default:a(()=>[l(k,{class:"back-icon"},{default:a(()=>[c("←")]),_:1})]),_:1},8,["onClick"]),l(q,{class:"navbar-title"},{default:a(()=>[c("好友申请")]),_:1}),l(q,{class:"navbar-right"})]),_:1})]),_:1}),o(" 好友申请列表 "),C.friendRequests.length>0?(t(),s(q,{key:0,class:"request-list"},{default:a(()=>[(t(!0),i(u,null,r(C.friendRequests,e=>(t(),s(q,{key:e.id,class:"request-item"},{default:a(()=>[l(q,{class:"user-avatar"},{default:a(()=>[l(k,{class:"avatar-text"},{default:a(()=>[c(f(E.getAvatarText(e.fromUser)),1)]),_:2},1024)]),_:2},1024),l(q,{class:"request-info"},{default:a(()=>[l(q,{class:"user-name"},{default:a(()=>[c(f(e.fromUser.nickname||e.fromUser.username),1)]),_:2},1024),l(q,{class:"request-message"},{default:a(()=>[c(f(e.message||"请求添加您为好友"),1)]),_:2},1024),l(q,{class:"request-time"},{default:a(()=>[c(f(E.formatTime(e.createTime)),1)]),_:2},1024)]),_:2},1024),"PENDING"===e.status?(t(),s(q,{key:0,class:"action-buttons"},{default:a(()=>[l(y,{onClick:t=>E.handleRequest(e,"REJECTED"),class:"reject-btn"},{default:a(()=>[c("拒绝")]),_:2},1032,["onClick"]),l(y,{onClick:t=>E.handleRequest(e,"ACCEPTED"),class:"accept-btn"},{default:a(()=>[c("同意")]),_:2},1032,["onClick"])]),_:2},1024)):(t(),s(q,{key:1,class:"status-text"},{default:a(()=>[l(k,{class:g(E.getStatusClass(e.status))},{default:a(()=>[c(f(E.getStatusText(e.status)),1)]),_:2},1032,["class"])]),_:2},1024))]),_:2},1024))),128))]),_:1})):C.loading?o("v-if",!0):(t(),i(u,{key:1},[o(" 空状态 "),l(q,{class:"empty-state"},{default:a(()=>[l(k,{class:"empty-icon"},{default:a(()=>[c("📭")]),_:1}),l(k,{class:"empty-text"},{default:a(()=>[c("暂无好友申请")]),_:1})]),_:1})],2112)),o(" 加载状态 "),C.loading?(t(),s(q,{key:2,class:"loading"},{default:a(()=>[l(k,null,{default:a(()=>[c("加载中...")]),_:1})]),_:1})):o("v-if",!0)]),_:1})}],["__scopeId","data-v-e271fe7d"]]);export{T as default};
