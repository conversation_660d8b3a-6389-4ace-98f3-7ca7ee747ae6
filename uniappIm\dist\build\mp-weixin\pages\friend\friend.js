"use strict";const e=require("../../common/vendor.js"),t=require("../../utils/api.js");require("../../utils/config.js");const s={components:{CustomTabBar:()=>"../../components/CustomTabBar.js"},data:()=>({currentTab:0,tabs:[{name:"好友列表",badge:0},{name:"收到申请",badge:0},{name:"发出申请",badge:0}],friendList:[],receivedRequests:[],sentRequests:[],showSearchModalFlag:!1,searchKeyword:"",searchResults:[],showRemarkModalFlag:!1,selectedFriend:{},remarkInput:"",saving:!1,friendSearchKeyword:""}),onLoad(){this.loadAllData()},computed:{filteredFriendList(){if(!this.friendSearchKeyword.trim())return this.friendList;const e=this.friendSearchKeyword.toLowerCase();return this.friendList.filter(t=>{const s=(t.remark||"").toLowerCase(),a=(t.nickname||"").toLowerCase(),r=(t.username||"").toLowerCase();return s.includes(e)||a.includes(e)||r.includes(e)})}},onShow(){this.loadAllData()},methods:{switchTab(e){this.currentTab=e},async loadAllData(){await Promise.all([this.loadFriendList(),this.loadReceivedRequests(),this.loadSentRequests()])},async loadFriendList(){try{const e=await t.getFriendList();200===e.code&&(this.friendList=e.data||[],this.tabs[0].badge=this.friendList.length)}catch(e){console.error("加载好友列表失败:",e)}},async loadReceivedRequests(){try{const e=await t.getReceivedFriendRequests();200===e.code&&(this.receivedRequests=e.data||[],this.tabs[1].badge=this.receivedRequests.filter(e=>"PENDING"===e.status).length)}catch(e){console.error("加载收到的申请失败:",e)}},async loadSentRequests(){try{const e=await t.getSentFriendRequests();200===e.code&&(this.sentRequests=e.data||[],this.tabs[2].badge=this.sentRequests.length)}catch(e){console.error("加载发出的申请失败:",e)}},async handleRequest(s,a){try{const r=await t.handleFriendRequest(s,a);200===r.code?(e.index.showToast({title:"accept"===a?"已同意":"已拒绝",icon:"success"}),this.loadAllData()):e.index.showToast({title:r.message,icon:"none"})}catch(r){e.index.showToast({title:"操作失败",icon:"none"})}},deleteFriendConfirm(t){e.index.showModal({title:"确认删除",content:`确定要删除好友 ${t.nickname||t.username} 吗？`,success:e=>{e.confirm&&this.deleteFriendAction(t.userId||t.id)}})},async deleteFriendAction(s){try{const a=await t.deleteFriend(s);200===a.code?(e.index.showToast({title:"删除成功",icon:"success"}),this.loadFriendList()):e.index.showToast({title:a.message,icon:"none"})}catch(a){e.index.showToast({title:"删除失败",icon:"none"})}},openChat(t){const s=t.userId||t.id,a=this.getDisplayName(t);e.index.navigateTo({url:`/pages/chatDetail/chatDetail?userId=${s}&nickname=${encodeURIComponent(a)}`})},getDisplayName:e=>e.remark&&e.remark.trim()?e.remark:e.nickname||e.username||"未知用户",showRemarkModal(e){this.selectedFriend=e,this.remarkInput=e.remark||"",this.showRemarkModalFlag=!0},hideRemarkModal(){this.showRemarkModalFlag=!1,this.selectedFriend={},this.remarkInput="",this.saving=!1},async saveRemark(){if(!this.saving){this.saving=!0;try{const s=await t.setFriendRemark(this.selectedFriend.userId,this.remarkInput.trim());200===s.code?(e.index.showToast({title:"备注设置成功",icon:"success"}),this.hideRemarkModal(),this.loadFriendList()):e.index.showToast({title:s.message||"设置失败",icon:"none"})}catch(s){console.error("设置备注失败:",s),e.index.showToast({title:"网络错误，请重试",icon:"none"})}finally{this.saving=!1}}},onFriendSearch(){},clearFriendSearch(){this.friendSearchKeyword=""},showSearchModal(){this.showSearchModalFlag=!0},hideSearchModal(){this.showSearchModalFlag=!1,this.searchKeyword="",this.searchResults=[]},onSearchInput(){this.searchKeyword.trim()||(this.searchResults=[])},async searchUsers(){if(this.searchKeyword.trim())try{const s=await t.searchUsers(this.searchKeyword);200===s.code?this.searchResults=s.data||[]:e.index.showToast({title:s.message,icon:"none"})}catch(s){e.index.showToast({title:"搜索失败",icon:"none"})}else e.index.showToast({title:"请输入搜索关键词",icon:"none"})},async sendFriendRequestToUser(s){try{const a=await t.sendFriendRequest({toUserId:s.userId||s.id,message:"请求添加您为好友"});200===a.code?(e.index.showToast({title:"申请已发送",icon:"success"}),this.hideSearchModal()):e.index.showToast({title:a.message,icon:"none"})}catch(a){e.index.showToast({title:"发送失败",icon:"none"})}},formatTime(e){const t=new Date(e),s=new Date-t;return s<6e4?"刚刚":s<36e5?Math.floor(s/6e4)+"分钟前":s<864e5?Math.floor(s/36e5)+"小时前":Math.floor(s/864e5)+"天前"},getStatusText:e=>({PENDING:"待处理",ACCEPTED:"已同意",REJECTED:"已拒绝"}[e]||e),getStatusClass:e=>({"status-pending":"PENDING"===e,"status-accepted":"ACCEPTED"===e,"status-rejected":"REJECTED"===e})}};if(!Array){e.resolveComponent("CustomTabBar")()}const a=e._export_sfc(s,[["render",function(t,s,a,r,i,n){return e.e({a:e.f(i.tabs,(t,s,a)=>e.e({a:e.t(t.name),b:t.badge>0},t.badge>0?{c:e.t(t.badge)}:{},{d:s,e:i.currentTab===s?1:"",f:e.o(e=>n.switchTab(s),s)})),b:0===i.currentTab},0===i.currentTab?e.e({c:e.o([e=>i.friendSearchKeyword=e.detail.value,(...e)=>n.onFriendSearch&&n.onFriendSearch(...e)]),d:i.friendSearchKeyword,e:i.friendSearchKeyword},i.friendSearchKeyword?{f:e.o((...e)=>n.clearFriendSearch&&n.clearFriendSearch(...e))}:{},{g:e.f(n.filteredFriendList,(t,s,a)=>e.e({a:e.t(n.getDisplayName(t).charAt(0)),b:e.t(n.getDisplayName(t)),c:t.remark},(t.remark,{}),{d:e.t(t.username),e:e.o(e=>n.showRemarkModal(t),t.userId),f:e.o(e=>n.deleteFriendConfirm(t),t.userId),g:t.userId,h:e.o(e=>n.openChat(t),t.userId)})),h:0===n.filteredFriendList.length},0===n.filteredFriendList.length?{i:e.t(i.friendSearchKeyword?"未找到相关好友":"暂无好友"),j:e.t(i.friendSearchKeyword?"尝试其他关键词":"点击右上角搜索添加好友")}:{}):{},{k:1===i.currentTab},1===i.currentTab?e.e({l:e.f(i.receivedRequests,(t,s,a)=>({a:e.t((t.fromUser.nickname||t.fromUser.username||"?").charAt(0)),b:e.t(t.fromUser.nickname||t.fromUser.username),c:e.t(t.message||"请求添加您为好友"),d:e.t(n.formatTime(t.createTime)),e:e.o(e=>n.handleRequest(t.id,"accept"),t.id),f:e.o(e=>n.handleRequest(t.id,"reject"),t.id),g:t.id})),m:0===i.receivedRequests.length},(i.receivedRequests.length,{})):{},{n:2===i.currentTab},2===i.currentTab?e.e({o:e.f(i.sentRequests,(t,s,a)=>({a:e.t((t.toUser.nickname||t.toUser.username||"?").charAt(0)),b:e.t(t.toUser.nickname||t.toUser.username),c:e.t(t.message||"请求添加为好友"),d:e.t(n.formatTime(t.createTime)),e:e.t(n.getStatusText(t.status)),f:e.n(n.getStatusClass(t.status)),g:t.id})),p:0===i.sentRequests.length},(i.sentRequests.length,{})):{},{q:i.showSearchModalFlag},i.showSearchModalFlag?e.e({r:e.o((...e)=>n.hideSearchModal&&n.hideSearchModal(...e)),s:e.o([e=>i.searchKeyword=e.detail.value,(...e)=>n.onSearchInput&&n.onSearchInput(...e)]),t:i.searchKeyword,v:e.o((...e)=>n.searchUsers&&n.searchUsers(...e)),w:e.f(i.searchResults,(t,s,a)=>({a:e.t((t.nickname||t.username||"?").charAt(0)),b:e.t(t.nickname||t.username),c:e.t(t.username),d:e.o(e=>n.sendFriendRequestToUser(t),t.id),e:t.id})),x:0===i.searchResults.length&&i.searchKeyword},(0===i.searchResults.length&&i.searchKeyword,{}),{y:e.o(()=>{}),z:e.o((...e)=>n.hideSearchModal&&n.hideSearchModal(...e))}):{},{A:i.showRemarkModalFlag},i.showRemarkModalFlag?{B:e.o((...e)=>n.hideRemarkModal&&n.hideRemarkModal(...e)),C:e.t(n.getDisplayName(i.selectedFriend).charAt(0)),D:e.t(i.selectedFriend.nickname||i.selectedFriend.username),E:e.t(i.selectedFriend.username),F:e.o((...e)=>n.saveRemark&&n.saveRemark(...e)),G:i.remarkInput,H:e.o(e=>i.remarkInput=e.detail.value),I:e.o((...e)=>n.hideRemarkModal&&n.hideRemarkModal(...e)),J:e.t(i.saving?"保存中...":"保存"),K:e.o((...e)=>n.saveRemark&&n.saveRemark(...e)),L:i.saving,M:e.o(()=>{}),N:e.o((...e)=>n.hideRemarkModal&&n.hideRemarkModal(...e))}:{},{O:e.p({"current-page":"friend"})})}],["__scopeId","data-v-4ff6f112"]]);wx.createPage(a);
