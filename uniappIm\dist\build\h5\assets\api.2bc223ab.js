const e={appName:"即时通讯",version:"1.0.0",pageSize:20,upload:{maxSize:10485760,allowTypes:["image/jpeg","image/png","image/gif"],allowResource:!0,allowRecourse:!0},message:{maxLength:1e3,retryTimes:3,timeout:5e3},permissions:{allowResource:!0,allowRecourse:!0,allowCamera:!0,allowLocation:!0}},o={development:{baseURL:"http://localhost:8080/api",wsURL:"ws://localhost:9999/ws",debug:!0},production:{baseURL:"https://your-api-domain.com/api",wsURL:"wss://your-websocket-domain.com/ws",debug:!1}};function a(){try{const a=function(){try{return"undefined"!=typeof window?"localhost"===window.location.hostname||"127.0.0.1"===window.location.hostname?"development":"production":"development"}catch(e){return console.error("获取环境失败:",e),"development"}}();console.log("当前环境:",a);const s=o[a]||o.development||{};if(!e)return console.error("baseConfig 未定义"),{appName:"即时通讯",version:"1.0.0",pageSize:20,baseURL:"http://localhost:8080/api",wsURL:"ws://localhost:9999/ws",debug:!0,upload:{maxSize:10485760,allowTypes:["image/jpeg","image/png","image/gif"],allowResource:!0,allowRecourse:!0},message:{maxLength:1e3,retryTimes:3,timeout:5e3},permissions:{allowResource:!0,allowRecourse:!0,allowCamera:!0,allowLocation:!0}};const r={...e,...s};return console.log("最终配置:",r),r}catch(a){return console.error("获取配置失败:",a),{appName:"即时通讯",version:"1.0.0",pageSize:20,baseURL:"http://localhost:8080/api",wsURL:"ws://localhost:9999/ws",debug:!0,upload:{maxSize:10485760,allowTypes:["image/jpeg","image/png","image/gif"],allowResource:!0,allowRecourse:!0},message:{maxLength:1e3,retryTimes:3,timeout:5e3},permissions:{allowResource:!0,allowRecourse:!0,allowCamera:!0,allowLocation:!0}}}}const s=(()=>{try{const e=a();return e?(e.upload||(e.upload={maxSize:10485760,allowTypes:["image/jpeg","image/png","image/gif"],allowResource:!0,allowRecourse:!0}),e.permissions||(e.permissions={allowResource:!0,allowRecourse:!0,allowCamera:!0,allowLocation:!0}),e.upload&&void 0===e.upload.allowRecourse&&(e.upload.allowRecourse=!0),e.permissions&&void 0===e.permissions.allowRecourse&&(e.permissions.allowRecourse=!0),e):(console.error("配置初始化失败，使用默认配置"),{appName:"即时通讯",version:"1.0.0",pageSize:20,baseURL:"http://localhost:8080/api",wsURL:"ws://localhost:9999/ws",debug:!0,upload:{maxSize:10485760,allowTypes:["image/jpeg","image/png","image/gif"],allowResource:!0,allowRecourse:!0},message:{maxLength:1e3,retryTimes:3,timeout:5e3},permissions:{allowResource:!0,allowRecourse:!0,allowCamera:!0,allowLocation:!0}})}catch(e){return console.error("配置导出失败:",e),{appName:"即时通讯",version:"1.0.0",pageSize:20,baseURL:"http://localhost:8080/api",wsURL:"ws://localhost:9999/ws",debug:!0,upload:{maxSize:10485760,allowTypes:["image/jpeg","image/png","image/gif"],allowResource:!0,allowRecourse:!0},message:{maxLength:1e3,retryTimes:3,timeout:5e3},permissions:{allowResource:!0,allowRecourse:!0,allowCamera:!0,allowLocation:!0}}}})();const r=function(){try{return s&&"object"==typeof s&&s.baseURL?s.baseURL:(console.warn("API baseURL未配置，使用默认地址"),"http://localhost:8080/api")}catch(e){return console.error("获取API baseURL失败:",e),"http://localhost:8080/api"}}();function t(e,o={}){return new Promise((a,s)=>{const t=uni.getStorageSync("token");uni.request({url:r+e,method:o.method||"GET",data:o.data,header:{"Content-Type":"application/json",Authorization:t?`Bearer ${t}`:"",...o.header},success:o=>{console.log("API请求成功:",e,o),a(o.data)},fail:a=>{console.error("API请求失败:",e,a);let r="网络请求失败";if(a.statusCode)switch(a.statusCode){case 401:r="登录已过期，请重新登录",uni.removeStorageSync("token"),uni.removeStorageSync("userInfo"),uni.reLaunch({url:"/pages/login/login"});break;case 403:r="没有权限访问";break;case 404:r="请求的资源不存在";break;case 500:r="服务器内部错误";break;default:r=a.errMsg||a.message||"网络请求失败"}else r=a.errMsg||a.message||"网络连接失败，请检查网络设置";const t=new Error(r);t.statusCode=a.statusCode,t.errMsg=a.errMsg,t.url=e,t.requestData=o.data,s(t)}})})}function n(e){return t("/auth/login",{method:"POST",data:e})}function l(e){return t("/auth/register",{method:"POST",data:e})}function i(e,o={}){let a=`/messages/history/${e}`;if(o.page||o.size){const e=new URLSearchParams;o.page&&e.append("page",o.page),o.size&&e.append("size",o.size),a+=`?${e.toString()}`}return t(a)}function u(e){return t("/messages/send",{method:"POST",data:e})}function c(e){return t(`/messages/session/${e}/read`,{method:"POST"})}function m(e){return t(`/friend/search?keyword=${encodeURIComponent(e)}`)}function p(e){return t("/friend/request",{method:"POST",data:e})}function g(){return t("/friend/requests/received")}function w(){return t("/friend/requests/sent")}function d(e,o){return t(`/friend/requests/${e}/${o}`,{method:"POST"})}function h(){return t("/friend/list")}function R(e){return t(`/friend/${e}`,{method:"DELETE"})}function f(e,o){return t(`/friend/${e}/remark`,{method:"PUT",data:{remark:o}})}function L(){return t("/friend/requests/count")}s?s.baseURL||console.error("API baseURL未配置，使用默认地址"):console.error("配置对象未正确加载，使用默认API地址");export{L as a,i as b,s as c,m as d,p as e,g as f,h as g,d as h,w as i,R as j,f as k,n as l,c as m,l as r,u as s};
