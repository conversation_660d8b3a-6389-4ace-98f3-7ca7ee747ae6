import{_ as e,o as a,c as s,w as t,i as l,a as n,b as c,e as r,r as i,F as o,d,t as u,f as h,g as f,I as m,S as _,h as k,j as y,k as w}from"./index-eb56c588.js";import{g as p,f as g,i as C,h as v,j as b,k as R,d as T,e as F}from"./api.2bc223ab.js";import{C as q}from"./CustomTabBar.ebabc49f.js";const S=e({components:{CustomTabBar:q},data:()=>({currentTab:0,tabs:[{name:"好友列表",badge:0},{name:"收到申请",badge:0},{name:"发出申请",badge:0}],friendList:[],receivedRequests:[],sentRequests:[],showSearchModalFlag:!1,searchKeyword:"",searchResults:[],showRemarkModalFlag:!1,selectedFriend:{},remarkInput:"",saving:!1,friendSearchKeyword:""}),onLoad(){this.loadAllData()},computed:{filteredFriendList(){if(!this.friendSearchKeyword.trim())return this.friendList;const e=this.friendSearchKeyword.toLowerCase();return this.friendList.filter(a=>{const s=(a.remark||"").toLowerCase(),t=(a.nickname||"").toLowerCase(),l=(a.username||"").toLowerCase();return s.includes(e)||t.includes(e)||l.includes(e)})}},onShow(){this.loadAllData()},methods:{switchTab(e){this.currentTab=e},async loadAllData(){await Promise.all([this.loadFriendList(),this.loadReceivedRequests(),this.loadSentRequests()])},async loadFriendList(){try{const e=await p();200===e.code&&(this.friendList=e.data||[],this.tabs[0].badge=this.friendList.length)}catch(e){console.error("加载好友列表失败:",e)}},async loadReceivedRequests(){try{const e=await g();200===e.code&&(this.receivedRequests=e.data||[],this.tabs[1].badge=this.receivedRequests.filter(e=>"PENDING"===e.status).length)}catch(e){console.error("加载收到的申请失败:",e)}},async loadSentRequests(){try{const e=await C();200===e.code&&(this.sentRequests=e.data||[],this.tabs[2].badge=this.sentRequests.length)}catch(e){console.error("加载发出的申请失败:",e)}},async handleRequest(e,a){try{const s=await v(e,a);200===s.code?(uni.showToast({title:"accept"===a?"已同意":"已拒绝",icon:"success"}),this.loadAllData()):uni.showToast({title:s.message,icon:"none"})}catch(s){uni.showToast({title:"操作失败",icon:"none"})}},deleteFriendConfirm(e){uni.showModal({title:"确认删除",content:`确定要删除好友 ${e.nickname||e.username} 吗？`,success:a=>{a.confirm&&this.deleteFriendAction(e.userId||e.id)}})},async deleteFriendAction(e){try{const a=await b(e);200===a.code?(uni.showToast({title:"删除成功",icon:"success"}),this.loadFriendList()):uni.showToast({title:a.message,icon:"none"})}catch(a){uni.showToast({title:"删除失败",icon:"none"})}},openChat(e){const a=e.userId||e.id,s=this.getDisplayName(e);uni.navigateTo({url:`/pages/chatDetail/chatDetail?userId=${a}&nickname=${encodeURIComponent(s)}`})},getDisplayName:e=>e.remark&&e.remark.trim()?e.remark:e.nickname||e.username||"未知用户",showRemarkModal(e){this.selectedFriend=e,this.remarkInput=e.remark||"",this.showRemarkModalFlag=!0},hideRemarkModal(){this.showRemarkModalFlag=!1,this.selectedFriend={},this.remarkInput="",this.saving=!1},async saveRemark(){if(!this.saving){this.saving=!0;try{const e=await R(this.selectedFriend.userId,this.remarkInput.trim());200===e.code?(uni.showToast({title:"备注设置成功",icon:"success"}),this.hideRemarkModal(),this.loadFriendList()):uni.showToast({title:e.message||"设置失败",icon:"none"})}catch(e){console.error("设置备注失败:",e),uni.showToast({title:"网络错误，请重试",icon:"none"})}finally{this.saving=!1}}},onFriendSearch(){},clearFriendSearch(){this.friendSearchKeyword=""},showSearchModal(){this.showSearchModalFlag=!0},hideSearchModal(){this.showSearchModalFlag=!1,this.searchKeyword="",this.searchResults=[]},onSearchInput(){this.searchKeyword.trim()||(this.searchResults=[])},async searchUsers(){if(this.searchKeyword.trim())try{const e=await T(this.searchKeyword);200===e.code?this.searchResults=e.data||[]:uni.showToast({title:e.message,icon:"none"})}catch(e){uni.showToast({title:"搜索失败",icon:"none"})}else uni.showToast({title:"请输入搜索关键词",icon:"none"})},async sendFriendRequestToUser(e){try{const a=await F({toUserId:e.userId||e.id,message:"请求添加您为好友"});200===a.code?(uni.showToast({title:"申请已发送",icon:"success"}),this.hideSearchModal()):uni.showToast({title:a.message,icon:"none"})}catch(a){uni.showToast({title:"发送失败",icon:"none"})}},formatTime(e){const a=new Date(e),s=new Date-a;return s<6e4?"刚刚":s<36e5?Math.floor(s/6e4)+"分钟前":s<864e5?Math.floor(s/36e5)+"小时前":Math.floor(s/864e5)+"天前"},getStatusText:e=>({PENDING:"待处理",ACCEPTED:"已同意",REJECTED:"已拒绝"}[e]||e),getStatusClass:e=>({"status-pending":"PENDING"===e,"status-accepted":"ACCEPTED"===e,"status-rejected":"REJECTED"===e})}},[["render",function(e,p,g,C,v,b){const R=f,T=l,F=m,q=_,S=k,I=y("CustomTabBar");return a(),s(T,{class:"friend-container"},{default:t(()=>[n(" 标签页 "),c(T,{class:"tabs"},{default:t(()=>[(a(!0),r(o,null,i(v.tabs,(e,l)=>(a(),s(T,{key:l,class:w(["tab-item",{active:v.currentTab===l}]),onClick:e=>b.switchTab(l)},{default:t(()=>[c(R,{class:"tab-text"},{default:t(()=>[d(u(e.name),1)]),_:2},1024),e.badge>0?(a(),s(R,{key:0,class:"tab-badge"},{default:t(()=>[d(u(e.badge),1)]),_:2},1024)):n("v-if",!0)]),_:2},1032,["class","onClick"]))),128))]),_:1}),n(" 内容区域 "),c(q,{class:"content","scroll-y":"true"},{default:t(()=>[n(" 好友列表 "),0===v.currentTab?(a(),s(T,{key:0,class:"friend-list"},{default:t(()=>[n(" 搜索框 "),c(T,{class:"search-section"},{default:t(()=>[c(T,{class:"search-box"},{default:t(()=>[c(T,{class:"search-icon"},{default:t(()=>[d("🔍")]),_:1}),c(F,{modelValue:v.friendSearchKeyword,"onUpdate:modelValue":p[0]||(p[0]=e=>v.friendSearchKeyword=e),class:"search-input",placeholder:"搜索好友（昵称/备注/用户名）",onInput:b.onFriendSearch},null,8,["modelValue","onInput"]),v.friendSearchKeyword?(a(),s(T,{key:0,class:"clear-icon",onClick:b.clearFriendSearch},{default:t(()=>[c(R,null,{default:t(()=>[d("×")]),_:1})]),_:1},8,["onClick"])):n("v-if",!0)]),_:1})]),_:1}),(a(!0),r(o,null,i(b.filteredFriendList,e=>(a(),s(T,{key:e.userId,class:"friend-item",onClick:a=>b.openChat(e)},{default:t(()=>[c(T,{class:"avatar"},{default:t(()=>[c(R,{class:"avatar-text"},{default:t(()=>[d(u(b.getDisplayName(e).charAt(0)),1)]),_:2},1024)]),_:2},1024),c(T,{class:"friend-info"},{default:t(()=>[c(T,{class:"friend-main-info"},{default:t(()=>[c(R,{class:"friend-name"},{default:t(()=>[d(u(b.getDisplayName(e)),1)]),_:2},1024),e.remark?(a(),s(R,{key:0,class:"friend-remark-tag"},{default:t(()=>[d("备注")]),_:1})):n("v-if",!0)]),_:2},1024),c(R,{class:"friend-username"},{default:t(()=>[d("@"+u(e.username),1)]),_:2},1024)]),_:2},1024),c(T,{class:"friend-actions"},{default:t(()=>[c(T,{class:"action-btn remark-btn",onClick:h(a=>b.showRemarkModal(e),["stop"])},{default:t(()=>[c(T,{class:"btn-icon"},{default:t(()=>[c(R,{class:"icon-text"},{default:t(()=>[d("备注")]),_:1})]),_:1})]),_:2},1032,["onClick"]),c(T,{class:"action-btn delete-btn",onClick:h(a=>b.deleteFriendConfirm(e),["stop"])},{default:t(()=>[c(T,{class:"btn-icon"},{default:t(()=>[c(R,{class:"icon-text"},{default:t(()=>[d("删除")]),_:1})]),_:1})]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1032,["onClick"]))),128)),0===b.filteredFriendList.length?(a(),s(T,{key:0,class:"empty-state"},{default:t(()=>[c(R,{class:"empty-text"},{default:t(()=>[d(u(v.friendSearchKeyword?"未找到相关好友":"暂无好友"),1)]),_:1}),c(R,{class:"empty-hint"},{default:t(()=>[d(u(v.friendSearchKeyword?"尝试其他关键词":"点击右上角搜索添加好友"),1)]),_:1})]),_:1})):n("v-if",!0)]),_:1})):n("v-if",!0),n(" 收到的申请 "),1===v.currentTab?(a(),s(T,{key:1,class:"request-list"},{default:t(()=>[(a(!0),r(o,null,i(v.receivedRequests,e=>(a(),s(T,{key:e.id,class:"request-item"},{default:t(()=>[c(T,{class:"avatar"},{default:t(()=>[c(R,{class:"avatar-text"},{default:t(()=>[d(u((e.fromUser.nickname||e.fromUser.username||"?").charAt(0)),1)]),_:2},1024)]),_:2},1024),c(T,{class:"request-info"},{default:t(()=>[c(R,{class:"request-name"},{default:t(()=>[d(u(e.fromUser.nickname||e.fromUser.username),1)]),_:2},1024),c(R,{class:"request-message"},{default:t(()=>[d(u(e.message||"请求添加您为好友"),1)]),_:2},1024),c(R,{class:"request-time"},{default:t(()=>[d(u(b.formatTime(e.createTime)),1)]),_:2},1024)]),_:2},1024),c(T,{class:"request-actions"},{default:t(()=>[c(T,{class:"accept-btn",onClick:a=>b.handleRequest(e.id,"accept")},{default:t(()=>[c(R,null,{default:t(()=>[d("同意")]),_:1})]),_:2},1032,["onClick"]),c(T,{class:"reject-btn",onClick:a=>b.handleRequest(e.id,"reject")},{default:t(()=>[c(R,null,{default:t(()=>[d("拒绝")]),_:1})]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024))),128)),0===v.receivedRequests.length?(a(),s(T,{key:0,class:"empty-state"},{default:t(()=>[c(R,{class:"empty-text"},{default:t(()=>[d("暂无好友申请")]),_:1})]),_:1})):n("v-if",!0)]),_:1})):n("v-if",!0),n(" 发出的申请 "),2===v.currentTab?(a(),s(T,{key:2,class:"request-list"},{default:t(()=>[(a(!0),r(o,null,i(v.sentRequests,e=>(a(),s(T,{key:e.id,class:"request-item"},{default:t(()=>[c(T,{class:"avatar"},{default:t(()=>[c(R,{class:"avatar-text"},{default:t(()=>[d(u((e.toUser.nickname||e.toUser.username||"?").charAt(0)),1)]),_:2},1024)]),_:2},1024),c(T,{class:"request-info"},{default:t(()=>[c(R,{class:"request-name"},{default:t(()=>[d(u(e.toUser.nickname||e.toUser.username),1)]),_:2},1024),c(R,{class:"request-message"},{default:t(()=>[d(u(e.message||"请求添加为好友"),1)]),_:2},1024),c(R,{class:"request-time"},{default:t(()=>[d(u(b.formatTime(e.createTime)),1)]),_:2},1024)]),_:2},1024),c(T,{class:"request-status"},{default:t(()=>[c(R,{class:w(["status-text",b.getStatusClass(e.status)])},{default:t(()=>[d(u(b.getStatusText(e.status)),1)]),_:2},1032,["class"])]),_:2},1024)]),_:2},1024))),128)),0===v.sentRequests.length?(a(),s(T,{key:0,class:"empty-state"},{default:t(()=>[c(R,{class:"empty-text"},{default:t(()=>[d("暂无发出的申请")]),_:1})]),_:1})):n("v-if",!0)]),_:1})):n("v-if",!0)]),_:1}),n(" 搜索弹窗 "),v.showSearchModalFlag?(a(),s(T,{key:0,class:"search-modal",onClick:b.hideSearchModal},{default:t(()=>[c(T,{class:"search-content",onClick:p[2]||(p[2]=h(()=>{},["stop"]))},{default:t(()=>[c(T,{class:"search-header"},{default:t(()=>[c(R,{class:"search-title"},{default:t(()=>[d("搜索用户")]),_:1}),c(T,{class:"close-btn",onClick:b.hideSearchModal},{default:t(()=>[c(R,null,{default:t(()=>[d("✕")]),_:1})]),_:1},8,["onClick"])]),_:1}),c(T,{class:"search-input-group"},{default:t(()=>[c(F,{modelValue:v.searchKeyword,"onUpdate:modelValue":p[1]||(p[1]=e=>v.searchKeyword=e),placeholder:"输入用户名或昵称",class:"search-input",onInput:b.onSearchInput},null,8,["modelValue","onInput"]),c(T,{class:"search-btn",onClick:b.searchUsers},{default:t(()=>[c(R,null,{default:t(()=>[d("搜索")]),_:1})]),_:1},8,["onClick"])]),_:1}),c(q,{class:"search-results","scroll-y":"true"},{default:t(()=>[(a(!0),r(o,null,i(v.searchResults,e=>(a(),s(T,{key:e.id,class:"search-item"},{default:t(()=>[c(T,{class:"avatar"},{default:t(()=>[c(R,{class:"avatar-text"},{default:t(()=>[d(u((e.nickname||e.username||"?").charAt(0)),1)]),_:2},1024)]),_:2},1024),c(T,{class:"user-info"},{default:t(()=>[c(R,{class:"user-name"},{default:t(()=>[d(u(e.nickname||e.username),1)]),_:2},1024),c(R,{class:"user-username"},{default:t(()=>[d("@"+u(e.username),1)]),_:2},1024)]),_:2},1024),c(T,{class:"add-btn",onClick:a=>b.sendFriendRequestToUser(e)},{default:t(()=>[c(R,null,{default:t(()=>[d("添加")]),_:1})]),_:2},1032,["onClick"])]),_:2},1024))),128)),0===v.searchResults.length&&v.searchKeyword?(a(),s(T,{key:0,class:"empty-state"},{default:t(()=>[c(R,{class:"empty-text"},{default:t(()=>[d("未找到相关用户")]),_:1})]),_:1})):n("v-if",!0)]),_:1})]),_:1})]),_:1},8,["onClick"])):n("v-if",!0),n(" 备注设置模态框 "),v.showRemarkModalFlag?(a(),s(T,{key:1,class:"modal-overlay",onClick:b.hideRemarkModal},{default:t(()=>[c(T,{class:"remark-modal",onClick:p[4]||(p[4]=h(()=>{},["stop"]))},{default:t(()=>[c(T,{class:"modal-header"},{default:t(()=>[c(R,{class:"modal-title"},{default:t(()=>[d("设置备注")]),_:1}),c(T,{class:"close-btn",onClick:b.hideRemarkModal},{default:t(()=>[c(R,{class:"close-icon"},{default:t(()=>[d("×")]),_:1})]),_:1},8,["onClick"])]),_:1}),c(T,{class:"modal-content"},{default:t(()=>[c(T,{class:"friend-preview"},{default:t(()=>[c(T,{class:"preview-avatar"},{default:t(()=>[c(R,{class:"preview-avatar-text"},{default:t(()=>[d(u(b.getDisplayName(v.selectedFriend).charAt(0)),1)]),_:1})]),_:1}),c(T,{class:"preview-info"},{default:t(()=>[c(R,{class:"preview-name"},{default:t(()=>[d(u(v.selectedFriend.nickname||v.selectedFriend.username),1)]),_:1}),c(R,{class:"preview-username"},{default:t(()=>[d("@"+u(v.selectedFriend.username),1)]),_:1})]),_:1})]),_:1}),c(T,{class:"input-section"},{default:t(()=>[c(R,{class:"input-label"},{default:t(()=>[d("备注名称")]),_:1}),c(F,{modelValue:v.remarkInput,"onUpdate:modelValue":p[3]||(p[3]=e=>v.remarkInput=e),class:"remark-input",placeholder:"请输入备注名称",maxlength:"20",focus:!0,"confirm-type":"done",onConfirm:b.saveRemark},null,8,["modelValue","onConfirm"])]),_:1})]),_:1}),c(T,{class:"modal-footer"},{default:t(()=>[c(S,{class:"cancel-btn",onClick:b.hideRemarkModal},{default:t(()=>[d("取消")]),_:1},8,["onClick"]),c(S,{class:"confirm-btn",onClick:b.saveRemark,disabled:v.saving},{default:t(()=>[d(u(v.saving?"保存中...":"保存"),1)]),_:1},8,["onClick","disabled"])]),_:1})]),_:1})]),_:1},8,["onClick"])):n("v-if",!0),n(" 自定义底部导航栏 "),c(I,{"current-page":"friend"})]),_:1})}],["__scopeId","data-v-4ff6f112"]]);export{S as default};
