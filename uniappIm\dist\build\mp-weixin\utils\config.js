"use strict";const e={appName:"即时通讯",version:"1.0.0",pageSize:20,upload:{maxSize:10485760,allowTypes:["image/jpeg","image/png","image/gif"],allowResource:!0,allowRecourse:!0},message:{maxLength:1e3,retryTimes:3,timeout:5e3},permissions:{allowResource:!0,allowRecourse:!0,allowCamera:!0,allowLocation:!0}},o={development:{baseURL:"http://localhost:8080/api",wsURL:"ws://localhost:9999/ws",debug:!0},production:{baseURL:"https://your-api-domain.com/api",wsURL:"wss://your-websocket-domain.com/ws",debug:!1}};const a=function(){try{const a=function(){try{return"development"}catch(e){return console.error("获取环境失败:",e),"development"}}();console.log("当前环境:",a);const s=o[a]||o.development||{};if(!e)return console.error("baseConfig 未定义"),{appName:"即时通讯",version:"1.0.0",pageSize:20,baseURL:"http://localhost:8080/api",wsURL:"ws://localhost:9999/ws",debug:!0,upload:{maxSize:10485760,allowTypes:["image/jpeg","image/png","image/gif"],allowResource:!0,allowRecourse:!0},message:{maxLength:1e3,retryTimes:3,timeout:5e3},permissions:{allowResource:!0,allowRecourse:!0,allowCamera:!0,allowLocation:!0}};const l={...e,...s};return console.log("最终配置:",l),l}catch(a){return console.error("获取配置失败:",a),{appName:"即时通讯",version:"1.0.0",pageSize:20,baseURL:"http://localhost:8080/api",wsURL:"ws://localhost:9999/ws",debug:!0,upload:{maxSize:10485760,allowTypes:["image/jpeg","image/png","image/gif"],allowResource:!0,allowRecourse:!0},message:{maxLength:1e3,retryTimes:3,timeout:5e3},permissions:{allowResource:!0,allowRecourse:!0,allowCamera:!0,allowLocation:!0}}}}();exports.config=a;
