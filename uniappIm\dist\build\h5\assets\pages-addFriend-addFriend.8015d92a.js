import{_ as e,o as s,c as a,w as t,i as n,a as o,b as l,d as c,e as i,r,F as u,t as d,g as h,I as m,h as f}from"./index-eb56c588.js";import{d as y,e as p}from"./api.2bc223ab.js";const _=e({data:()=>({searchKeyword:"",searchResults:[],loading:!1,showEmpty:!1,emptyText:"请输入关键词搜索用户"}),methods:{goBack(){uni.navigateBack()},onSearchInput(){this.searchKeyword.trim()||(this.searchResults=[],this.showEmpty=!1)},async handleSearch(){if(this.searchKeyword.trim()){this.loading=!0,this.showEmpty=!1;try{if(!uni.getStorageSync("userInfo"))return void uni.showToast({title:"请先登录",icon:"none"});const e=await y(this.searchKeyword.trim());console.log("搜索结果:",e),200===e.code?(this.searchResults=e.data.map(e=>({...e,requestSent:!1})),0===this.searchResults.length&&(this.showEmpty=!0,this.emptyText="未找到相关用户")):(uni.showToast({title:e.message||"搜索失败",icon:"none"}),this.showEmpty=!0,this.emptyText="搜索失败，请重试")}catch(e){console.error("搜索用户失败:",e),uni.showToast({title:"网络错误，请重试",icon:"none"}),this.showEmpty=!0,this.emptyText="网络错误，请重试"}finally{this.loading=!1}}else uni.showToast({title:"请输入搜索关键词",icon:"none"})},async sendFriendRequest(e){try{if(!uni.getStorageSync("userInfo"))return void uni.showToast({title:"请先登录",icon:"none"});const s=await p({toUserId:e.userId,message:"我想加你为好友"});console.log("发送好友申请结果:",s),200===s.code?(e.requestSent=!0,this.$forceUpdate(),uni.showToast({title:"好友申请已发送",icon:"success"})):uni.showToast({title:s.message||"发送失败",icon:"none"})}catch(s){console.error("发送好友申请失败:",s),uni.showToast({title:"网络错误，请重试",icon:"none"})}}}},[["render",function(e,y,p,_,w,g){const k=h,b=n,v=m,T=f;return s(),a(b,{class:"container"},{default:t(()=>[o(" 自定义导航栏 "),l(b,{class:"custom-navbar"},{default:t(()=>[l(b,{class:"navbar-content"},{default:t(()=>[l(b,{class:"navbar-left",onClick:g.goBack},{default:t(()=>[l(k,{class:"back-icon"},{default:t(()=>[c("←")]),_:1})]),_:1},8,["onClick"]),l(b,{class:"navbar-title"},{default:t(()=>[c("添加好友")]),_:1}),l(b,{class:"navbar-right"})]),_:1})]),_:1}),o(" 搜索区域 "),l(b,{class:"search-section"},{default:t(()=>[l(b,{class:"search-box"},{default:t(()=>[l(v,{modelValue:w.searchKeyword,"onUpdate:modelValue":y[0]||(y[0]=e=>w.searchKeyword=e),placeholder:"请输入用户名或手机号",class:"search-input",onInput:g.onSearchInput},null,8,["modelValue","onInput"]),l(T,{onClick:g.handleSearch,class:"search-btn"},{default:t(()=>[c("搜索")]),_:1},8,["onClick"])]),_:1})]),_:1}),o(" 搜索结果 "),w.searchResults.length>0?(s(),a(b,{key:0,class:"search-results"},{default:t(()=>[l(b,{class:"result-title"},{default:t(()=>[c("搜索结果")]),_:1}),(s(!0),i(u,null,r(w.searchResults,e=>(s(),a(b,{key:e.userId,class:"user-item"},{default:t(()=>[l(b,{class:"user-avatar"},{default:t(()=>[l(k,{class:"avatar-text"},{default:t(()=>[c(d(e.nickname?e.nickname.charAt(0):e.username.charAt(0)),1)]),_:2},1024)]),_:2},1024),l(b,{class:"user-info"},{default:t(()=>[l(b,{class:"user-name"},{default:t(()=>[c(d(e.nickname||e.username),1)]),_:2},1024),l(b,{class:"user-username"},{default:t(()=>[c("用户名: "+d(e.username),1)]),_:2},1024)]),_:2},1024),l(T,{onClick:s=>g.sendFriendRequest(e),class:"add-btn",disabled:e.requestSent},{default:t(()=>[c(d(e.requestSent?"已发送":"添加"),1)]),_:2},1032,["onClick","disabled"])]),_:2},1024))),128))]),_:1})):o("v-if",!0),o(" 空状态 "),w.showEmpty?(s(),a(b,{key:1,class:"empty-state"},{default:t(()=>[l(k,{class:"empty-text"},{default:t(()=>[c(d(w.emptyText),1)]),_:1})]),_:1})):o("v-if",!0),o(" 加载状态 "),w.loading?(s(),a(b,{key:2,class:"loading"},{default:t(()=>[l(k,null,{default:t(()=>[c("搜索中...")]),_:1})]),_:1})):o("v-if",!0)]),_:1})}],["__scopeId","data-v-bb3d159a"]]);export{_ as default};
