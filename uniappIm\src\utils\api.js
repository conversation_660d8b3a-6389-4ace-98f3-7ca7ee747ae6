// 导入配置
import { config } from './config.js'

// API配置 - 添加多层防御性检查
function getBaseURL() {
  try {
    if (config && typeof config === 'object' && config.baseURL) {
      return config.baseURL
    }

    console.warn('API baseURL未配置，使用默认地址')
    return 'http://localhost:8080/api'
  } catch (error) {
    console.error('获取API baseURL失败:', error)
    return 'http://localhost:8080/api'
  }
}

const BASE_URL = getBaseURL()

// 添加配置检查
if (!config) {
  console.error('配置对象未正确加载，使用默认API地址')
} else if (!config.baseURL) {
  console.error('API baseURL未配置，使用默认地址')
}

/**
 * 统一请求封装
 * @param {string} url 请求地址
 * @param {object} options 请求选项
 * @returns {Promise} 请求Promise
 */
function request(url, options = {}) {
  return new Promise((resolve, reject) => {
    // 获取存储的token
    const token = uni.getStorageSync('token')
    
    // 发起请求
    uni.request({
      url: BASE_URL + url,
      method: options.method || 'GET',
      data: options.data,
      header: {
        'Content-Type': 'application/json',
        'Authorization': token ? `Bearer ${token}` : '',
        ...options.header
      },
      success: (res) => {
        console.log('API请求成功:', url, res)
        resolve(res.data)
      },
      fail: (err) => {
        console.error('API请求失败:', url, err)

        // 处理不同类型的错误
        let errorMsg = '网络请求失败'

        if (err.statusCode) {
          switch (err.statusCode) {
            case 401:
              errorMsg = '登录已过期，请重新登录'
              // 清除本地存储的token和用户信息
              uni.removeStorageSync('token')
              uni.removeStorageSync('userInfo')
              // 跳转到登录页面
              uni.reLaunch({
                url: '/pages/login/login'
              })
              break
            case 403:
              errorMsg = '没有权限访问'
              break
            case 404:
              errorMsg = '请求的资源不存在'
              break
            case 500:
              errorMsg = '服务器内部错误'
              break
            default:
              errorMsg = err.errMsg || err.message || '网络请求失败'
          }
        } else {
          errorMsg = err.errMsg || err.message || '网络连接失败，请检查网络设置'
        }

        const error = new Error(errorMsg)
        error.statusCode = err.statusCode
        error.errMsg = err.errMsg
        error.url = url
        error.requestData = options.data
        reject(error)
      }
    })
  })
}

/**
 * 用户登录
 * @param {object} data 登录数据 {username, password}
 * @returns {Promise} 登录结果
 */
export function login(data) {
  return request('/auth/login', {
    method: 'POST',
    data
  })
}

/**
 * 用户注册
 * @param {object} data 注册数据 {username, password, nickname}
 * @returns {Promise} 注册结果
 */
export function register(data) {
  return request('/auth/register', {
    method: 'POST',
    data
  })
}

/**
 * 用户退出登录
 * @returns {Promise} 退出结果
 */
export function logout() {
  return request('/auth/logout', {
    method: 'POST'
  })
}

/**
 * 获取用户列表
 * @returns {Promise} 用户列表
 */
export function getUserList() {
  return request('/auth/users')
}

/**
 * 获取聊天历史记录
 * @param {number} toUserId 对方用户ID
 * @param {object} params 分页参数 {page, size}
 * @returns {Promise} 聊天记录
 */
export function getChatHistory(toUserId, params = {}) {
  let url = `/messages/history/${toUserId}`
  if (params.page || params.size) {
    const queryParams = new URLSearchParams()
    if (params.page) queryParams.append('page', params.page)
    if (params.size) queryParams.append('size', params.size)
    url += `?${queryParams.toString()}`
  }
  return request(url)
}

/**
 * 标记消息为已读
 * @param {number} messageId 消息ID
 * @returns {Promise} 操作结果
 */
export function markMessageAsRead(messageId) {
  return request(`/messages/${messageId}/read`, {
    method: 'POST'
  })
}

/**
 * 发送消息
 * @param {object} data 消息数据 {toUserId, content}
 * @returns {Promise} 发送结果
 */
export function sendMessage(data) {
  return request('/messages/send', {
    method: 'POST',
    data
  })
}

/**
 * 标记会话为已读
 * @param {number} sessionId 会话ID
 * @returns {Promise} 操作结果
 */
export function markSessionAsRead(sessionId) {
  return request(`/messages/session/${sessionId}/read`, {
    method: 'POST'
  })
}

// ==================== 好友管理API ====================

/**
 * 搜索用户
 * @param {string} keyword 搜索关键词
 * @returns {Promise} 用户列表
 */
export function searchUsers(keyword) {
  return request(`/friend/search?keyword=${encodeURIComponent(keyword)}`)
}

/**
 * 发送好友申请
 * @param {object} data 申请数据 {toUserId, message}
 * @returns {Promise} 申请结果
 */
export function sendFriendRequest(data) {
  return request('/friend/request', {
    method: 'POST',
    data
  })
}

/**
 * 获取收到的好友申请列表
 * @returns {Promise} 好友申请列表
 */
export function getReceivedFriendRequests() {
  return request('/friend/requests/received')
}

/**
 * 获取发出的好友申请列表
 * @returns {Promise} 好友申请列表
 */
export function getSentFriendRequests() {
  return request('/friend/requests/sent')
}

/**
 * 处理好友申请
 * @param {number} requestId 申请ID
 * @param {string} action 操作类型 'accept' | 'reject'
 * @returns {Promise} 处理结果
 */
export function handleFriendRequest(requestId, action) {
  return request(`/friend/requests/${requestId}/${action}`, {
    method: 'POST'
  })
}

/**
 * 获取好友列表
 * @returns {Promise} 好友列表
 */
export function getFriendList() {
  return request('/friend/list')
}

/**
 * 删除好友
 * @param {number} friendId 好友ID
 * @returns {Promise} 删除结果
 */
export function deleteFriend(friendId) {
  return request(`/friend/${friendId}`, {
    method: 'DELETE'
  })
}

/**
 * 设置好友备注
 * @param {number} friendId 好友ID
 * @param {string} remark 备注名称
 * @returns {Promise} 设置结果
 */
export function setFriendRemark(friendId, remark) {
  return request(`/friend/${friendId}/remark`, {
    method: 'PUT',
    data: { remark }
  })
}

/**
 * 检查是否是好友
 * @param {number} friendId 好友ID
 * @returns {Promise} 是否是好友
 */
export function isFriend(friendId) {
  return request(`/friend/check/${friendId}`)
}

/**
 * 获取待处理的好友申请数量
 * @returns {Promise} 申请数量
 */
export function getPendingRequestCount() {
  return request('/friend/requests/count')
}

// ==================== 系统统计API ====================

/**
 * 获取系统统计信息
 * @returns {Promise} 统计信息
 */
export function getSystemStats() {
  return request('/stats')
}

/**
 * 获取在线用户列表
 * @returns {Promise} 在线用户列表
 */
export function getOnlineUsers() {
  return request('/stats/online-users')
}

/**
 * 检查用户是否在线
 * @param {number} userId 用户ID
 * @returns {Promise} 在线状态
 */
export function checkUserOnline(userId) {
  return request(`/stats/user-online/${userId}`)
}

// ==================== 扩展功能API（可选） ====================

/**
 * 获取数据库状态（用于测试）
 * @returns {Promise} 请求Promise
 */
export function getDatabaseStatus() {
  return request('/auth/db-status', {
    method: 'GET'
  })
}

/**
 * 系统健康检查
 * @returns {Promise} 健康状态
 */
export function healthCheck() {
  return request('/health')
}