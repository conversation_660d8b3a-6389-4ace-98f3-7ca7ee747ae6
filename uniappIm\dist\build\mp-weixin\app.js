"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const o=require("./common/vendor.js");Math;const n={onLaunch:function(){console.log("App Launch")},onShow:function(){console.log("App Show")},onHide:function(){console.log("App Hide")}};const e=o._export_sfc(n,[["render",function(o,n,e,t,p,c){return{}}]]);function t(){return{app:o.createSSRApp(e)}}t().app.mount("#app"),exports.createApp=t;
