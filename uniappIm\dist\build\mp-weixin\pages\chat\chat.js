"use strict";const e=require("../../common/vendor.js"),n=require("../../utils/api.js"),t=require("../../utils/websocket.js");require("../../utils/config.js");const o={components:{CustomTabBar:()=>"../../components/CustomTabBar.js"},data:()=>({userInfo:null,userList:[],showAddMenuModal:!1,pendingRequestCount:0,loading:!1}),onLoad(){this.checkLogin(),this.loadUserInfo(),this.loadFriendList(),this.loadPendingRequestCount(),this.initWebSocket()},onUnload(){t.closeWebSocket()},onShow(){this.loadFriendList(),this.loadPendingRequestCount()},methods:{checkLogin(){e.index.getStorageSync("token")||e.index.reLaunch({url:"/pages/login/login"})},loadUserInfo(){this.userInfo=e.index.getStorageSync("userInfo")},async loadFriendList(){this.loading=!0;try{console.log("=== 开始加载好友列表 ===");const t=await n.getFriendList();if(console.log("好友列表API响应:",t),200===t.code){const e=t.data||[];console.log("原始好友数据:",e),this.userList=e.map(e=>({...e,id:e.userId||e.id,userId:e.userId||e.id,nickname:e.nickname||e.username,username:e.username,lastMessage:"点击开始聊天",lastMessageTime:new Date,unreadCount:0})),console.log("处理后的好友列表:",this.userList)}else console.error("获取好友列表失败:",t.message),e.index.showToast({title:t.message||"获取好友列表失败",icon:"none"})}catch(t){console.error("获取好友列表失败:",t),e.index.showToast({title:"网络错误: "+t.message,icon:"none"})}finally{this.loading=!1}},async loadPendingRequestCount(){try{const e=await n.getPendingRequestCount();200===e.code&&(this.pendingRequestCount=e.data||0)}catch(e){console.error("获取待处理申请数量失败:",e)}},initWebSocket(){const n=e.index.getStorageSync("token");n&&t.connectWebSocket(n,e=>{if(console.log("收到消息:",e),"chat"===e.type){const n=this.userList.findIndex(n=>(n.userId||n.id)===e.fromUserId);if(-1!==n){this.userList[n].lastMessage=e.content,this.userList[n].lastMessageTime=new Date,this.userList[n].unreadCount=(this.userList[n].unreadCount||0)+1;const t=this.userList.splice(n,1)[0];this.userList.unshift(t)}}})},openChat(n){n.unreadCount=0;const t=n.userId||n.id,o=n.nickname||n.username||"未知用户";console.log("打开聊天:",{userId:t,nickname:o,user:n}),e.index.navigateTo({url:`/pages/chatDetail/chatDetail?userId=${t}&nickname=${encodeURIComponent(o)}`})},showAddMenu(){this.showAddMenuModal=!0},hideAddMenu(){this.showAddMenuModal=!1},addFriend(){this.hideAddMenu(),e.index.navigateTo({url:"/pages/addFriend/addFriend"})},openFriendManage(){this.hideAddMenu(),e.index.navigateTo({url:"/pages/friendRequests/friendRequests"})},logout(){this.hideAddMenu(),e.index.showModal({title:"提示",content:"确定要退出登录吗？",success:n=>{n.confirm&&(e.index.removeStorageSync("token"),e.index.removeStorageSync("userInfo"),e.index.reLaunch({url:"/pages/login/login"}))}})},formatTime(e){if(!e)return"";const n=new Date,t=new Date(e),o=n-t;if(o<6e4)return"刚刚";if(o<36e5)return Math.floor(o/6e4)+"分钟前";if(o<864e5)return Math.floor(o/36e5)+"小时前";const s=new Date(n.getFullYear(),n.getMonth(),n.getDate()),i=new Date(s.getTime()-864e5);return t>=s?t.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit"}):t>=i?"昨天":t.toLocaleDateString("zh-CN",{month:"2-digit",day:"2-digit"})}}};if(!Array){e.resolveComponent("CustomTabBar")()}const s=e._export_sfc(o,[["render",function(n,t,o,s,i,d){return e.e({a:e.o((...e)=>d.showAddMenu&&d.showAddMenu(...e)),b:e.f(i.userList,(n,t,o)=>({a:e.t(n.nickname.charAt(0)),b:e.t(n.nickname),c:e.t(d.formatTime(n.lastMessageTime)),d:e.t(n.lastMessage||"点击开始聊天"),e:n.id,f:e.o(e=>d.openChat(n),n.id)})),c:i.loading},(i.loading,{}),{d:!i.loading&&0===i.userList.length},(i.loading||i.userList.length,{}),{e:i.showAddMenuModal},i.showAddMenuModal?e.e({f:e.o((...e)=>d.addFriend&&d.addFriend(...e)),g:i.pendingRequestCount>0},i.pendingRequestCount>0?{h:e.t(i.pendingRequestCount>99?"99+":i.pendingRequestCount)}:{},{i:e.o((...e)=>d.openFriendManage&&d.openFriendManage(...e)),j:e.o((...e)=>d.logout&&d.logout(...e)),k:e.o(()=>{}),l:e.o((...e)=>d.hideAddMenu&&d.hideAddMenu(...e))}):{},{m:e.p({"current-page":"chat"})})}],["__scopeId","data-v-151731cc"]]);wx.createPage(s);
