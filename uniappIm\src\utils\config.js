// 获取当前环境
function getEnv() {
  try {
    // #ifdef H5
    if (typeof window !== 'undefined') {
      const isDev = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1'
      return isDev ? 'development' : 'production'
    }
    // #endif

    // #ifdef MP-WEIXIN
    return 'development' // 小程序默认使用开发环境
    // #endif

    // #ifdef APP-PLUS
    return 'production' // App默认使用生产环境
    // #endif

    // 默认返回开发环境
    return 'development'
  } catch (error) {
    console.error('获取环境失败:', error)
    return 'development'
  }
}

// 基础配置
const baseConfig = {
  // 应用信息
  appName: '即时通讯',
  version: '1.0.0',

  // 分页配置
  pageSize: 20,

  // 文件上传配置
  upload: {
    maxSize: 10 * 1024 * 1024, // 10MB
    allowTypes: ['image/jpeg', 'image/png', 'image/gif'],
    allowResource: true,
    allowRecourse: true
  },

  // 消息配置
  message: {
    maxLength: 1000, // 消息最大长度
    retryTimes: 3,   // 重试次数
    timeout: 5000    // 超时时间(ms)
  },

  // 权限配置
  permissions: {
    allowResource: true,
    allowRecourse: true,
    allowCamera: true,
    allowLocation: true
  }
}

// 环境配置
const envConfig = {
  development: {
    baseURL: 'http://localhost:8080/api',
    wsURL: 'ws://localhost:9999/ws',
    debug: true
  },
  production: {
    baseURL: 'https://your-api-domain.com/api',
    wsURL: 'wss://your-websocket-domain.com/ws',
    debug: false
  }
}

// 获取当前环境配置
export function getCurrentConfig() {
  try {
    const env = getEnv()
    console.log('当前环境:', env)

    // 确保环境配置存在
    const envConf = envConfig[env] || envConfig.development || {}

    // 确保基础配置存在
    if (!baseConfig) {
      console.error('baseConfig 未定义')
      return getDefaultConfig()
    }

    const config = {
      ...baseConfig,
      ...envConf
    }

    console.log('最终配置:', config)
    return config
  } catch (error) {
    console.error('获取配置失败:', error)
    return getDefaultConfig()
  }
}

// 默认配置（防御性编程）
function getDefaultConfig() {
  return {
    appName: '即时通讯',
    version: '1.0.0',
    pageSize: 20,
    baseURL: 'http://localhost:8080/api',
    wsURL: 'ws://localhost:9999/ws',
    debug: true,
    upload: {
      maxSize: 10 * 1024 * 1024,
      allowTypes: ['image/jpeg', 'image/png', 'image/gif'],
      allowResource: true,
      allowRecourse: true
    },
    message: {
      maxLength: 1000,
      retryTimes: 3,
      timeout: 5000
    },
    permissions: {
      allowResource: true,
      allowRecourse: true,
      allowCamera: true,
      allowLocation: true
    }
  }
}

// 导出配置 - 使用立即执行函数确保安全初始化
export const config = (() => {
  try {
    const result = getCurrentConfig()
    if (!result) {
      console.error('配置初始化失败，使用默认配置')
      return getDefaultConfig()
    }

    // 确保关键属性存在
    if (!result.upload) {
      result.upload = {
        maxSize: 10 * 1024 * 1024,
        allowTypes: ['image/jpeg', 'image/png', 'image/gif'],
        allowResource: true,
        allowRecourse: true
      }
    }

    if (!result.permissions) {
      result.permissions = {
        allowResource: true,
        allowRecourse: true,
        allowCamera: true,
        allowLocation: true
      }
    }

    // 确保 allowRecourse 属性存在
    if (result.upload && typeof result.upload.allowRecourse === 'undefined') {
      result.upload.allowRecourse = true
    }

    if (result.permissions && typeof result.permissions.allowRecourse === 'undefined') {
      result.permissions.allowRecourse = true
    }

    return result
  } catch (error) {
    console.error('配置导出失败:', error)
    return getDefaultConfig()
  }
})()
