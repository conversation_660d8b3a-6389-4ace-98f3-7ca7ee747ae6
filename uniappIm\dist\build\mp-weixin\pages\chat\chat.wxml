<view class="chat-container data-v-151731cc"><view class="header data-v-151731cc"><view class="header-content data-v-151731cc"><text class="title data-v-151731cc">即时通讯</text><view class="header-right data-v-151731cc"><view class="add-icon data-v-151731cc" bindtap="{{a}}"><text class="icon data-v-151731cc">+</text></view></view></view></view><scroll-view class="chat-list data-v-151731cc" scroll-y="true"><view wx:for="{{b}}" wx:for-item="user" wx:key="e" class="chat-item data-v-151731cc" bindtap="{{user.f}}"><view class="avatar data-v-151731cc"><text class="avatar-text data-v-151731cc">{{user.a}}</text></view><view class="chat-content data-v-151731cc"><view class="chat-header data-v-151731cc"><text class="friend-name data-v-151731cc">{{user.b}}</text><text class="chat-time data-v-151731cc">{{user.c}}</text></view><text class="last-message data-v-151731cc">{{user.d}}</text></view></view></scroll-view><view wx:if="{{c}}" class="loading-state data-v-151731cc"><text class="data-v-151731cc">加载中...</text></view><view wx:if="{{d}}" class="empty-state data-v-151731cc"><text class="empty-text data-v-151731cc">暂无好友</text><text class="empty-hint data-v-151731cc">点击右上角"+"添加好友开始聊天</text></view><view wx:if="{{e}}" class="add-menu-modal data-v-151731cc" bindtap="{{l}}"><view class="add-menu data-v-151731cc" catchtap="{{k}}"><view class="add-menu-item data-v-151731cc" bindtap="{{f}}"><view class="menu-item-content data-v-151731cc"><text class="add-menu-icon data-v-151731cc">👤</text><text class="add-menu-text data-v-151731cc">添加好友</text></view></view><view class="add-menu-item data-v-151731cc" bindtap="{{i}}"><view class="menu-item-content data-v-151731cc"><text class="add-menu-icon data-v-151731cc">👥</text><text class="add-menu-text data-v-151731cc">好友申请</text><text wx:if="{{g}}" class="badge data-v-151731cc">{{h}}</text></view></view><view class="add-menu-item data-v-151731cc" bindtap="{{j}}"><view class="menu-item-content data-v-151731cc"><text class="add-menu-icon data-v-151731cc">🚪</text><text class="add-menu-text data-v-151731cc">退出登录</text></view></view></view></view><custom-tab-bar wx:if="{{m}}" class="data-v-151731cc" u-i="151731cc-0" bind:__l="__l" u-p="{{m}}"/></view>