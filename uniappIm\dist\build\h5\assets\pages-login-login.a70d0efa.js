import{_ as s,o as e,c as a,w as l,i as o,a as t,b as n,d as i,t as c,e as u,r,F as d,f,g,I as m,h as p,n as h}from"./index-eb56c588.js";import{l as _,r as w}from"./api.2bc223ab.js";const k=s({data:()=>({loading:!1,showRegisterModal:!1,showDevTools:!1,loginForm:{username:"",password:""},registerForm:{username:"",password:"",nickname:""},testUsers:[{username:"test",password:"test123",nickname:"测试用户",color:"#667eea"},{username:"zhang",password:"123456",nickname:"张三",color:"#f093fb"},{username:"li",password:"123456",nickname:"李四",color:"#4facfe"}]}),methods:{async handleLogin(){if(this.loginForm.username&&this.loginForm.password){this.loading=!0;try{console.log("=== 开始登录 ==="),console.log("登录表单数据:",this.loginForm),console.log("API地址:","http://localhost:8080/api/auth/login");const s=await _(this.loginForm);console.log("登录API响应:",s),200===s.code?(uni.setStorageSync("token",s.data.token),uni.setStorageSync("userInfo",s.data.user),console.log("登录成功，保存的用户信息:",s.data.user),uni.showToast({title:"登录成功",icon:"success"}),uni.reLaunch({url:"/pages/chat/chat"})):(console.log("登录失败:",s.message),uni.showToast({title:s.message||"登录失败",icon:"none"}))}catch(s){console.error("登录异常:",s),uni.showToast({title:"网络错误，请检查后端服务: "+s.message,icon:"none"})}finally{this.loading=!1}}else uni.showToast({title:"请填写完整信息",icon:"none"})},handleRegister(){this.showRegisterModal=!0},closeRegisterPopup(){this.showRegisterModal=!1,this.registerForm={username:"",password:"",nickname:""}},async submitRegister(){if(this.registerForm.username&&this.registerForm.password)try{const s=await w(this.registerForm);200===s.code?(uni.showToast({title:"注册成功",icon:"success"}),this.closeRegisterPopup(),this.loginForm.username=this.registerForm.username,this.loginForm.password=this.registerForm.password):uni.showToast({title:s.message,icon:"none"})}catch(s){uni.showToast({title:"注册失败",icon:"none"})}else uni.showToast({title:"请填写完整信息",icon:"none"})},async testConnection(){uni.showLoading({title:"测试连接中..."});try{await uni.request({url:"http://localhost:8080/api/test",method:"GET",timeout:5e3});uni.hideLoading(),uni.showToast({title:"连接成功",icon:"success"})}catch(s){uni.hideLoading(),uni.showToast({title:"连接失败",icon:"none"})}},async quickLogin(s){this.loginForm.username=s.username,this.loginForm.password=s.password,await this.handleLogin()},toggleDevTools(){this.showDevTools=!this.showDevTools}}},[["render",function(s,_,w,k,F,v){const b=o,y=g,C=m,T=p;return e(),a(b,{class:"login-container"},{default:l(()=>[t(" 登录头部 "),n(b,{class:"login-header"},{default:l(()=>[n(b,{class:"logo-section"},{default:l(()=>[n(b,{class:"logo-container",onLongpress:v.toggleDevTools},{default:l(()=>[n(b,{class:"logo-icon"},{default:l(()=>[i("🤖")]),_:1}),n(b,{class:"logo-glow"})]),_:1},8,["onLongpress"]),n(y,{class:"brand-name"},{default:l(()=>[i("伴伴AI")]),_:1}),n(y,{class:"brand-slogan"},{default:l(()=>[i("智能陪伴，温暖相随")]),_:1})]),_:1}),n(b,{class:"floating-elements"},{default:l(()=>[n(b,{class:"float-item float-1"},{default:l(()=>[i("✨")]),_:1}),n(b,{class:"float-item float-2"},{default:l(()=>[i("💫")]),_:1}),n(b,{class:"float-item float-3"},{default:l(()=>[i("🌟")]),_:1})]),_:1})]),_:1}),t(" 登录表单 "),n(b,{class:"login-form"},{default:l(()=>[n(b,{class:"form-title"},{default:l(()=>[n(y,{class:"welcome-text"},{default:l(()=>[i("欢迎回来")]),_:1}),n(y,{class:"welcome-subtitle"},{default:l(()=>[i("登录您的伴伴AI账户")]),_:1})]),_:1}),n(b,{class:"input-group"},{default:l(()=>[n(b,{class:"input-icon"},{default:l(()=>[i("👤")]),_:1}),n(C,{modelValue:F.loginForm.username,"onUpdate:modelValue":_[0]||(_[0]=s=>F.loginForm.username=s),placeholder:"请输入用户名",class:"input-field"},null,8,["modelValue"])]),_:1}),n(b,{class:"input-group"},{default:l(()=>[n(b,{class:"input-icon"},{default:l(()=>[i("🔒")]),_:1}),n(C,{modelValue:F.loginForm.password,"onUpdate:modelValue":_[1]||(_[1]=s=>F.loginForm.password=s),placeholder:"请输入密码",type:"password",class:"input-field"},null,8,["modelValue"])]),_:1}),n(T,{onClick:v.handleLogin,class:"login-btn",disabled:F.loading},{default:l(()=>[n(b,{class:"btn-content"},{default:l(()=>[F.loading?(e(),a(y,{key:0,class:"loading-icon"},{default:l(()=>[i("⏳")]),_:1})):t("v-if",!0),n(y,{class:"btn-text"},{default:l(()=>[i(c(F.loading?"登录中...":"开始AI之旅"),1)]),_:1})]),_:1})]),_:1},8,["onClick","disabled"]),n(b,{class:"divider"},{default:l(()=>[n(b,{class:"divider-line"}),n(y,{class:"divider-text"},{default:l(()=>[i("或")]),_:1}),n(b,{class:"divider-line"})]),_:1}),n(b,{class:"register-link"},{default:l(()=>[n(y,{onClick:v.handleRegister},{default:l(()=>[i("还没有账号？立即注册")]),_:1},8,["onClick"])]),_:1})]),_:1}),t(" 开发者工具部分 - 仅在开发环境显示 "),F.showDevTools?(e(),a(b,{key:0,class:"dev-tools-section"},{default:l(()=>[n(b,{class:"dev-tools-header"},{default:l(()=>[n(b,{class:"dev-icon"},{default:l(()=>[i("🔧")]),_:1}),n(y,{class:"dev-title"},{default:l(()=>[i("开发者工具")]),_:1})]),_:1}),n(T,{onClick:v.testConnection,class:"dev-btn test-btn"},{default:l(()=>[n(y,{class:"dev-btn-icon"},{default:l(()=>[i("🔗")]),_:1}),n(y,{class:"dev-btn-text"},{default:l(()=>[i("测试连接")]),_:1})]),_:1},8,["onClick"]),n(b,{class:"quick-login-section"},{default:l(()=>[n(b,{class:"quick-login-header"},{default:l(()=>[n(b,{class:"quick-icon"},{default:l(()=>[i("👥")]),_:1}),n(y,{class:"quick-title"},{default:l(()=>[i("快速登录")]),_:1})]),_:1}),n(b,{class:"test-users"},{default:l(()=>[(e(!0),u(d,null,r(F.testUsers,s=>(e(),a(b,{key:s.username,class:"test-user-item",onClick:e=>v.quickLogin(s)},{default:l(()=>[n(b,{class:"user-avatar",style:h({backgroundColor:s.color})},{default:l(()=>[n(y,{class:"user-avatar-text"},{default:l(()=>[i(c(s.nickname.charAt(0)),1)]),_:2},1024)]),_:2},1032,["style"]),n(b,{class:"user-info"},{default:l(()=>[n(y,{class:"user-nickname"},{default:l(()=>[i(c(s.nickname),1)]),_:2},1024),n(y,{class:"user-username"},{default:l(()=>[i(c(s.username)+"/"+c(s.password),1)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onClick"]))),128))]),_:1})]),_:1})]),_:1})):t("v-if",!0),t(" 注册弹窗 "),F.showRegisterModal?(e(),a(b,{key:1,class:"modal-overlay",onClick:v.closeRegisterPopup},{default:l(()=>[n(b,{class:"register-popup",onClick:_[5]||(_[5]=f(()=>{},["stop"]))},{default:l(()=>[n(b,{class:"popup-header"},{default:l(()=>[n(b,{class:"popup-icon"},{default:l(()=>[i("🎉")]),_:1}),n(b,{class:"popup-title"},{default:l(()=>[i("加入伴伴AI")]),_:1}),n(b,{class:"popup-subtitle"},{default:l(()=>[i("开启您的AI陪伴之旅")]),_:1})]),_:1}),n(b,{class:"input-group"},{default:l(()=>[n(b,{class:"input-icon"},{default:l(()=>[i("👤")]),_:1}),n(C,{modelValue:F.registerForm.username,"onUpdate:modelValue":_[2]||(_[2]=s=>F.registerForm.username=s),placeholder:"请输入用户名",class:"input-field"},null,8,["modelValue"])]),_:1}),n(b,{class:"input-group"},{default:l(()=>[n(b,{class:"input-icon"},{default:l(()=>[i("🔒")]),_:1}),n(C,{modelValue:F.registerForm.password,"onUpdate:modelValue":_[3]||(_[3]=s=>F.registerForm.password=s),placeholder:"请输入密码",type:"password",class:"input-field"},null,8,["modelValue"])]),_:1}),n(b,{class:"input-group"},{default:l(()=>[n(b,{class:"input-icon"},{default:l(()=>[i("✨")]),_:1}),n(C,{modelValue:F.registerForm.nickname,"onUpdate:modelValue":_[4]||(_[4]=s=>F.registerForm.nickname=s),placeholder:"请输入昵称（可选）",class:"input-field"},null,8,["modelValue"])]),_:1}),n(b,{class:"popup-buttons"},{default:l(()=>[n(T,{onClick:v.closeRegisterPopup,class:"cancel-btn"},{default:l(()=>[n(y,null,{default:l(()=>[i("取消")]),_:1})]),_:1},8,["onClick"]),n(T,{onClick:v.submitRegister,class:"confirm-btn"},{default:l(()=>[n(y,{class:"confirm-icon"},{default:l(()=>[i("🚀")]),_:1}),n(y,null,{default:l(()=>[i("立即注册")]),_:1})]),_:1},8,["onClick"])]),_:1})]),_:1})]),_:1},8,["onClick"])):t("v-if",!0)]),_:1})}],["__scopeId","data-v-a637cc87"]]);export{k as default};
