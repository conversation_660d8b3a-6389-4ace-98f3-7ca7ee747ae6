import{c as e}from"./api.2bc223ab.js";let o=null,t=null,c=null,n=null,l=0,s=!1;const r=function(){try{return e&&"object"==typeof e&&e.wsURL?e.wsURL:(console.warn("WebSocket URL未配置，使用默认地址"),"ws://localhost:9999/ws")}catch(o){return console.error("获取WebSocket URL失败:",o),"ws://localhost:9999/ws"}}();function a(e,S){o&&o.close(),t=S,o=uni.connectSocket({url:r,success:()=>{console.log("WebSocket连接成功")},fail:e=>{console.error("WebSocket连接失败:",e)}}),o.onOpen(()=>{console.log("WebSocket已打开"),l=0,s=!1,u({type:"auth",token:e}),n=setInterval(()=>{u({type:"heartbeat"})},3e4)}),o.onMessage(e=>{try{const o=JSON.parse(e.data);console.log("收到WebSocket消息:",o),"auth_success"===o.type?console.log("WebSocket认证成功"):"heartbeat"===o.type?console.log("心跳响应"):t&&t(o)}catch(o){console.error("解析WebSocket消息失败:",o)}}),o.onClose(()=>{if(console.log("WebSocket连接关闭"),b(),!s&&l<5&&!c){l++;const o=Math.min(1e3*Math.pow(2,l),3e4);console.log(`第${l}次重连WebSocket，${o}ms后重试`),c=setTimeout(()=>{a(e,t),c=null},o)}else l>=5&&(console.error("WebSocket重连次数已达上限，停止重连"),uni.showToast({title:"网络连接失败，请检查网络",icon:"none"}))}),o.onError(e=>{console.error("WebSocket错误:",e)})}function u(e){o&&1===o.readyState?o.send({data:JSON.stringify(e),success:()=>{console.log("WebSocket消息发送成功:",e)},fail:e=>{console.error("WebSocket消息发送失败:",e)}}):console.error("WebSocket未连接")}function S(){s=!0,b(),c&&(clearTimeout(c),c=null),o&&(o.close(),o=null),l=0}function b(){n&&(clearInterval(n),n=null)}e?e.wsURL||console.error("WebSocket URL未配置，使用默认地址"):console.error("配置对象未正确加载，使用默认WebSocket地址");export{S as a,a as c,u as s};
