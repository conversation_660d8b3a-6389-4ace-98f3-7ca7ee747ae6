<view class="container data-v-e271fe7d"><view class="custom-navbar data-v-e271fe7d"><view class="navbar-content data-v-e271fe7d"><view class="navbar-left data-v-e271fe7d" bindtap="{{a}}"><text class="back-icon data-v-e271fe7d">←</text></view><view class="navbar-title data-v-e271fe7d">好友申请</view><view class="navbar-right data-v-e271fe7d"></view></view></view><view wx:if="{{b}}" class="request-list data-v-e271fe7d"><view wx:for="{{c}}" wx:for-item="request" wx:key="j" class="request-item data-v-e271fe7d"><view class="user-avatar data-v-e271fe7d"><text class="avatar-text data-v-e271fe7d">{{request.a}}</text></view><view class="request-info data-v-e271fe7d"><view class="user-name data-v-e271fe7d">{{request.b}}</view><view class="request-message data-v-e271fe7d">{{request.c}}</view><view class="request-time data-v-e271fe7d">{{request.d}}</view></view><view wx:if="{{request.e}}" class="action-buttons data-v-e271fe7d"><button bindtap="{{request.f}}" class="reject-btn data-v-e271fe7d">拒绝</button><button bindtap="{{request.g}}" class="accept-btn data-v-e271fe7d">同意</button></view><view wx:else class="status-text data-v-e271fe7d"><text class="{{['data-v-e271fe7d', request.i]}}">{{request.h}}</text></view></view></view><view wx:elif="{{d}}" class="empty-state data-v-e271fe7d"><text class="empty-icon data-v-e271fe7d">📭</text><text class="empty-text data-v-e271fe7d">暂无好友申请</text></view><view wx:if="{{e}}" class="loading data-v-e271fe7d"><text class="data-v-e271fe7d">加载中...</text></view></view>