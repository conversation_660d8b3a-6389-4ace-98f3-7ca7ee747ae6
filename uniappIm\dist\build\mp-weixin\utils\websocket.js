"use strict";const e=require("../common/vendor.js"),o=require("./config.js");let c=null,n=null,t=null,s=null,l=0,r=!1;const a=o.config&&o.config.wsURL?o.config.wsURL:"ws://localhost:9999/ws";function i(e){c&&1===c.readyState?c.send({data:JSON.stringify(e),success:()=>{console.log("WebSocket消息发送成功:",e)},fail:e=>{console.error("WebSocket消息发送失败:",e)}}):console.error("WebSocket未连接")}function u(){s&&(clearInterval(s),s=null)}o.config?o.config.wsURL||console.error("WebSocket URL未配置，使用默认地址"):console.error("配置对象未正确加载，使用默认WebSocket地址"),exports.closeWebSocket=function(){r=!0,u(),t&&(clearTimeout(t),t=null),c&&(c.close(),c=null),l=0},exports.connectWebSocket=function o(S,b){c&&c.close(),n=b,c=e.index.connectSocket({url:a,success:()=>{console.log("WebSocket连接成功")},fail:e=>{console.error("WebSocket连接失败:",e)}}),c.onOpen(()=>{console.log("WebSocket已打开"),l=0,r=!1,i({type:"auth",token:S}),s=setInterval(()=>{i({type:"heartbeat"})},3e4)}),c.onMessage(e=>{try{const o=JSON.parse(e.data);console.log("收到WebSocket消息:",o),"auth_success"===o.type?console.log("WebSocket认证成功"):"heartbeat"===o.type?console.log("心跳响应"):n&&n(o)}catch(o){console.error("解析WebSocket消息失败:",o)}}),c.onClose(()=>{if(console.log("WebSocket连接关闭"),u(),!r&&l<5&&!t){l++;const e=Math.min(1e3*Math.pow(2,l),3e4);console.log(`第${l}次重连WebSocket，${e}ms后重试`),t=setTimeout(()=>{o(S,n),t=null},e)}else l>=5&&(console.error("WebSocket重连次数已达上限，停止重连"),e.index.showToast({title:"网络连接失败，请检查网络",icon:"none"}))}),c.onError(e=>{console.error("WebSocket错误:",e)})},exports.sendWebSocketMessage=i;
