"use strict";const e=require("../common/vendor.js"),r=require("./config.js"),t=r.config&&r.config.baseURL?r.config.baseURL:"http://localhost:8080/api";function n(r,n={}){return new Promise((s,o)=>{const a=e.index.getStorageSync("token");e.index.request({url:t+r,method:n.method||"GET",data:n.data,header:{"Content-Type":"application/json",Authorization:a?`Bearer ${a}`:"",...n.header},success:e=>{console.log("API请求成功:",r,e),s(e.data)},fail:t=>{console.error("API请求失败:",r,t);let s="网络请求失败";if(t.statusCode)switch(t.statusCode){case 401:s="登录已过期，请重新登录",e.index.removeStorageSync("token"),e.index.removeStorageSync("userInfo"),e.index.reLaunch({url:"/pages/login/login"});break;case 403:s="没有权限访问";break;case 404:s="请求的资源不存在";break;case 500:s="服务器内部错误";break;default:s=t.errMsg||t.message||"网络请求失败"}else s=t.errMsg||t.message||"网络连接失败，请检查网络设置";const a=new Error(s);a.statusCode=t.statusCode,a.errMsg=t.errMsg,a.url=r,a.requestData=n.data,o(a)}})})}r.config?r.config.baseURL||console.error("API baseURL未配置，使用默认地址"):console.error("配置对象未正确加载，使用默认API地址"),exports.deleteFriend=function(e){return n(`/friend/${e}`,{method:"DELETE"})},exports.getChatHistory=function(e,r={}){let t=`/messages/history/${e}`;if(r.page||r.size){const e=new URLSearchParams;r.page&&e.append("page",r.page),r.size&&e.append("size",r.size),t+=`?${e.toString()}`}return n(t)},exports.getFriendList=function(){return n("/friend/list")},exports.getPendingRequestCount=function(){return n("/friend/requests/count")},exports.getReceivedFriendRequests=function(){return n("/friend/requests/received")},exports.getSentFriendRequests=function(){return n("/friend/requests/sent")},exports.handleFriendRequest=function(e,r){return n(`/friend/requests/${e}/${r}`,{method:"POST"})},exports.login=function(e){return n("/auth/login",{method:"POST",data:e})},exports.markSessionAsRead=function(e){return n(`/messages/session/${e}/read`,{method:"POST"})},exports.register=function(e){return n("/auth/register",{method:"POST",data:e})},exports.searchUsers=function(e){return n(`/friend/search?keyword=${encodeURIComponent(e)}`)},exports.sendFriendRequest=function(e){return n("/friend/request",{method:"POST",data:e})},exports.sendMessage=function(e){return n("/messages/send",{method:"POST",data:e})},exports.setFriendRemark=function(e,r){return n(`/friend/${e}/remark`,{method:"PUT",data:{remark:r}})};
