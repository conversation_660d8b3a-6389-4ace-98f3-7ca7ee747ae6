"use strict";const e=require("../../common/vendor.js"),t=require("../../utils/api.js");require("../../utils/config.js");const s={data:()=>({searchKeyword:"",searchResults:[],loading:!1,showEmpty:!1,emptyText:"请输入关键词搜索用户"}),methods:{goBack(){e.index.navigateBack()},onSearchInput(){this.searchKeyword.trim()||(this.searchResults=[],this.showEmpty=!1)},async handleSearch(){if(this.searchKeyword.trim()){this.loading=!0,this.showEmpty=!1;try{if(!e.index.getStorageSync("userInfo"))return void e.index.showToast({title:"请先登录",icon:"none"});const s=await t.searchUsers(this.searchKeyword.trim());console.log("搜索结果:",s),200===s.code?(this.searchResults=s.data.map(e=>({...e,requestSent:!1})),0===this.searchResults.length&&(this.showEmpty=!0,this.emptyText="未找到相关用户")):(e.index.showToast({title:s.message||"搜索失败",icon:"none"}),this.showEmpty=!0,this.emptyText="搜索失败，请重试")}catch(s){console.error("搜索用户失败:",s),e.index.showToast({title:"网络错误，请重试",icon:"none"}),this.showEmpty=!0,this.emptyText="网络错误，请重试"}finally{this.loading=!1}}else e.index.showToast({title:"请输入搜索关键词",icon:"none"})},async sendFriendRequest(s){try{if(!e.index.getStorageSync("userInfo"))return void e.index.showToast({title:"请先登录",icon:"none"});const n=await t.sendFriendRequest({toUserId:s.userId,message:"我想加你为好友"});console.log("发送好友申请结果:",n),200===n.code?(s.requestSent=!0,this.$forceUpdate(),e.index.showToast({title:"好友申请已发送",icon:"success"})):e.index.showToast({title:n.message||"发送失败",icon:"none"})}catch(n){console.error("发送好友申请失败:",n),e.index.showToast({title:"网络错误，请重试",icon:"none"})}}}};const n=e._export_sfc(s,[["render",function(t,s,n,o,a,i){return e.e({a:e.o((...e)=>i.goBack&&i.goBack(...e)),b:e.o([e=>a.searchKeyword=e.detail.value,(...e)=>i.onSearchInput&&i.onSearchInput(...e)]),c:a.searchKeyword,d:e.o((...e)=>i.handleSearch&&i.handleSearch(...e)),e:a.searchResults.length>0},a.searchResults.length>0?{f:e.f(a.searchResults,(t,s,n)=>({a:e.t(t.nickname?t.nickname.charAt(0):t.username.charAt(0)),b:e.t(t.nickname||t.username),c:e.t(t.username),d:e.t(t.requestSent?"已发送":"添加"),e:e.o(e=>i.sendFriendRequest(t),t.userId),f:t.requestSent,g:t.userId}))}:{},{g:a.showEmpty},a.showEmpty?{h:e.t(a.emptyText)}:{},{i:a.loading},(a.loading,{}))}],["__scopeId","data-v-bb3d159a"]]);wx.createPage(n);
