import{_ as a,o as t,c as s,w as e,b as c,d as r,k as i,g as l,i as n}from"./index-eb56c588.js";const d=a({name:"CustomTabBar",props:{currentPage:{type:String,default:"chat"}},methods:{switchTab(a){a!==this.currentPage&&(console.log("切换到页面:",a),"chat"===a?uni.redirectTo({url:"/pages/chat/chat"}):"friend"===a&&uni.redirectTo({url:"/pages/friend/friend"}))}}},[["render",function(a,d,o,u,f,b){const _=l,h=n;return t(),s(h,{class:"custom-tabbar"},{default:e(()=>[c(h,{class:i(["tab-item",{active:"chat"===o.currentPage}]),onClick:d[0]||(d[0]=a=>b.switchTab("chat"))},{default:e(()=>[c(h,{class:"tab-icon"},{default:e(()=>[c(_,{class:"icon"},{default:e(()=>[r("💬")]),_:1})]),_:1}),c(_,{class:"tab-text"},{default:e(()=>[r("聊天")]),_:1})]),_:1},8,["class"]),c(h,{class:i(["tab-item",{active:"friend"===o.currentPage}]),onClick:d[1]||(d[1]=a=>b.switchTab("friend"))},{default:e(()=>[c(h,{class:"tab-icon"},{default:e(()=>[c(_,{class:"icon"},{default:e(()=>[r("👥")]),_:1})]),_:1}),c(_,{class:"tab-text"},{default:e(()=>[r("好友")]),_:1})]),_:1},8,["class"])]),_:1})}],["__scopeId","data-v-b20bf1a0"]]);export{d as C};
