<view class="friend-container data-v-4ff6f112"><view class="tabs data-v-4ff6f112"><view wx:for="{{a}}" wx:for-item="tab" wx:key="d" class="{{['tab-item', 'data-v-4ff6f112', tab.e && 'active']}}" bindtap="{{tab.f}}"><text class="tab-text data-v-4ff6f112">{{tab.a}}</text><text wx:if="{{tab.b}}" class="tab-badge data-v-4ff6f112">{{tab.c}}</text></view></view><scroll-view class="content data-v-4ff6f112" scroll-y="true"><view wx:if="{{b}}" class="friend-list data-v-4ff6f112"><view class="search-section data-v-4ff6f112"><view class="search-box data-v-4ff6f112"><view class="search-icon data-v-4ff6f112">🔍</view><input class="search-input data-v-4ff6f112" placeholder="搜索好友（昵称/备注/用户名）" bindinput="{{c}}" value="{{d}}"/><view wx:if="{{e}}" class="clear-icon data-v-4ff6f112" bindtap="{{f}}"><text class="data-v-4ff6f112">×</text></view></view></view><view wx:for="{{g}}" wx:for-item="friend" wx:key="g" class="friend-item data-v-4ff6f112" bindtap="{{friend.h}}"><view class="avatar data-v-4ff6f112"><text class="avatar-text data-v-4ff6f112">{{friend.a}}</text></view><view class="friend-info data-v-4ff6f112"><view class="friend-main-info data-v-4ff6f112"><text class="friend-name data-v-4ff6f112">{{friend.b}}</text><text wx:if="{{friend.c}}" class="friend-remark-tag data-v-4ff6f112">备注</text></view><text class="friend-username data-v-4ff6f112">@{{friend.d}}</text></view><view class="friend-actions data-v-4ff6f112"><view class="action-btn remark-btn data-v-4ff6f112" catchtap="{{friend.e}}"><view class="btn-icon data-v-4ff6f112"><text class="icon-text data-v-4ff6f112">备注</text></view></view><view class="action-btn delete-btn data-v-4ff6f112" catchtap="{{friend.f}}"><view class="btn-icon data-v-4ff6f112"><text class="icon-text data-v-4ff6f112">删除</text></view></view></view></view><view wx:if="{{h}}" class="empty-state data-v-4ff6f112"><text class="empty-text data-v-4ff6f112">{{i}}</text><text class="empty-hint data-v-4ff6f112">{{j}}</text></view></view><view wx:if="{{k}}" class="request-list data-v-4ff6f112"><view wx:for="{{l}}" wx:for-item="request" wx:key="g" class="request-item data-v-4ff6f112"><view class="avatar data-v-4ff6f112"><text class="avatar-text data-v-4ff6f112">{{request.a}}</text></view><view class="request-info data-v-4ff6f112"><text class="request-name data-v-4ff6f112">{{request.b}}</text><text class="request-message data-v-4ff6f112">{{request.c}}</text><text class="request-time data-v-4ff6f112">{{request.d}}</text></view><view class="request-actions data-v-4ff6f112"><view class="accept-btn data-v-4ff6f112" bindtap="{{request.e}}"><text class="data-v-4ff6f112">同意</text></view><view class="reject-btn data-v-4ff6f112" bindtap="{{request.f}}"><text class="data-v-4ff6f112">拒绝</text></view></view></view><view wx:if="{{m}}" class="empty-state data-v-4ff6f112"><text class="empty-text data-v-4ff6f112">暂无好友申请</text></view></view><view wx:if="{{n}}" class="request-list data-v-4ff6f112"><view wx:for="{{o}}" wx:for-item="request" wx:key="g" class="request-item data-v-4ff6f112"><view class="avatar data-v-4ff6f112"><text class="avatar-text data-v-4ff6f112">{{request.a}}</text></view><view class="request-info data-v-4ff6f112"><text class="request-name data-v-4ff6f112">{{request.b}}</text><text class="request-message data-v-4ff6f112">{{request.c}}</text><text class="request-time data-v-4ff6f112">{{request.d}}</text></view><view class="request-status data-v-4ff6f112"><text class="{{['status-text', 'data-v-4ff6f112', request.f]}}">{{request.e}}</text></view></view><view wx:if="{{p}}" class="empty-state data-v-4ff6f112"><text class="empty-text data-v-4ff6f112">暂无发出的申请</text></view></view></scroll-view><view wx:if="{{q}}" class="search-modal data-v-4ff6f112" bindtap="{{z}}"><view class="search-content data-v-4ff6f112" catchtap="{{y}}"><view class="search-header data-v-4ff6f112"><text class="search-title data-v-4ff6f112">搜索用户</text><view class="close-btn data-v-4ff6f112" bindtap="{{r}}"><text class="data-v-4ff6f112">✕</text></view></view><view class="search-input-group data-v-4ff6f112"><input placeholder="输入用户名或昵称" class="search-input data-v-4ff6f112" bindinput="{{s}}" value="{{t}}"/><view class="search-btn data-v-4ff6f112" bindtap="{{v}}"><text class="data-v-4ff6f112">搜索</text></view></view><scroll-view class="search-results data-v-4ff6f112" scroll-y="true"><view wx:for="{{w}}" wx:for-item="user" wx:key="e" class="search-item data-v-4ff6f112"><view class="avatar data-v-4ff6f112"><text class="avatar-text data-v-4ff6f112">{{user.a}}</text></view><view class="user-info data-v-4ff6f112"><text class="user-name data-v-4ff6f112">{{user.b}}</text><text class="user-username data-v-4ff6f112">@{{user.c}}</text></view><view class="add-btn data-v-4ff6f112" bindtap="{{user.d}}"><text class="data-v-4ff6f112">添加</text></view></view><view wx:if="{{x}}" class="empty-state data-v-4ff6f112"><text class="empty-text data-v-4ff6f112">未找到相关用户</text></view></scroll-view></view></view><view wx:if="{{A}}" class="modal-overlay data-v-4ff6f112" bindtap="{{N}}"><view class="remark-modal data-v-4ff6f112" catchtap="{{M}}"><view class="modal-header data-v-4ff6f112"><text class="modal-title data-v-4ff6f112">设置备注</text><view class="close-btn data-v-4ff6f112" bindtap="{{B}}"><text class="close-icon data-v-4ff6f112">×</text></view></view><view class="modal-content data-v-4ff6f112"><view class="friend-preview data-v-4ff6f112"><view class="preview-avatar data-v-4ff6f112"><text class="preview-avatar-text data-v-4ff6f112">{{C}}</text></view><view class="preview-info data-v-4ff6f112"><text class="preview-name data-v-4ff6f112">{{D}}</text><text class="preview-username data-v-4ff6f112">@{{E}}</text></view></view><view class="input-section data-v-4ff6f112"><text class="input-label data-v-4ff6f112">备注名称</text><input class="remark-input data-v-4ff6f112" placeholder="请输入备注名称" maxlength="20" focus="{{true}}" confirm-type="done" bindconfirm="{{F}}" value="{{G}}" bindinput="{{H}}"/></view></view><view class="modal-footer data-v-4ff6f112"><button class="cancel-btn data-v-4ff6f112" bindtap="{{I}}">取消</button><button class="confirm-btn data-v-4ff6f112" bindtap="{{K}}" disabled="{{L}}">{{J}}</button></view></view></view><custom-tab-bar wx:if="{{O}}" class="data-v-4ff6f112" u-i="4ff6f112-0" bind:__l="__l" u-p="{{O}}"/></view>